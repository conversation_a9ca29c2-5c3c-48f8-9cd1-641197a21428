-- 修復 admin 用戶的密碼
-- 密碼: admin123 (已加密)

UPDATE users 
SET password_hash = '$2b$12$LQv3c1yqBwuvHpS7TbuOCOFCXDaa8rjDM.6zSaOOgAgF6aLK6nEUq', -- admin123
    pharmacy_name = 'Admin Pharmacy',
    email = '<EMAIL>',
    role_id = (SELECT id FROM roles WHERE name = 'admin' LIMIT 1),
    is_active = true,
    status = 'approved',
    updated_at = CURRENT_TIMESTAMP
WHERE username = 'admin';

-- 如果 admin 用戶不存在，創建一個
INSERT INTO users (
    username, 
    email, 
    password_hash, 
    role_id, 
    pharmacy_name,
    is_active,
    status,
    created_at, 
    updated_at
) 
SELECT 
    'admin',
    '<EMAIL>',
    '$2b$12$LQv3c1yqBwuvHpS7TbuOCOFCXDaa8rjDM.6zSaOOgAgF6aLK6nEUq',  -- admin123
    r.id,
    'Admin Pharmacy',
    true,
    'approved',
    CURRENT_TIMESTAMP,
    CURRENT_TIMESTAMP
FROM roles r 
WHERE r.name = 'admin'
ON CONFLICT (username) DO UPDATE SET
    password_hash = EXCLUDED.password_hash,
    role_id = EXCLUDED.role_id,
    is_active = true,
    status = 'approved',
    updated_at = CURRENT_TIMESTAMP;

-- 驗證結果
SELECT 
    u.id, 
    u.username, 
    u.email, 
    u.pharmacy_name,
    u.is_active,
    u.status,
    r.name as role_name 
FROM users u 
LEFT JOIN roles r ON u.role_id = r.id 
WHERE u.username = 'admin';
