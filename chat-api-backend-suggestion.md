# 聊天功能後端 API 建議實現

## 資料庫結構

### 聊天訊息表 (chat_messages)
```sql
CREATE TABLE chat_messages (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    user_id VARCHAR(255) NOT NULL,
    user_name VARCHAR(255) NOT NULL,
    admin_id VARCHAR(255) NULL,
    admin_name VARCHAR(255) NULL,
    message TEXT NOT NULL,
    type ENUM('user', 'admin') NOT NULL,
    replied BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    status ENUM('sent', 'read', 'resolved') DEFAULT 'sent',
    
    INDEX idx_user_id (user_id),
    INDEX idx_type (type),
    INDEX idx_created_at (created_at),
    INDEX idx_replied (replied)
);
```

## API 端點

### 1. 用戶發送訊息
```
POST /api/chat/messages
Authorization: Bearer {token}

Request Body:
{
    "message": "請問這個產品有庫存嗎？",
    "type": "user"
}

Response:
{
    "success": true,
    "data": {
        "id": 123,
        "user_id": "pharmacy_001",
        "user_name": "健康藥局",
        "message": "請問這個產品有庫存嗎？",
        "type": "user",
        "created_at": "2025-01-13T10:30:00Z",
        "status": "sent"
    }
}
```

### 2. 用戶載入自己的聊天記錄
```
GET /api/chat/messages?user_id={user_id}
Authorization: Bearer {token}

Response:
{
    "success": true,
    "data": [
        {
            "id": 123,
            "user_id": "pharmacy_001",
            "user_name": "健康藥局",
            "message": "請問這個產品有庫存嗎？",
            "type": "user",
            "created_at": "2025-01-13T10:30:00Z",
            "replied": false
        },
        {
            "id": 124,
            "user_id": "pharmacy_001",
            "admin_id": "admin_001",
            "admin_name": "客服人員",
            "message": "目前有庫存，您需要多少數量？",
            "type": "admin",
            "created_at": "2025-01-13T10:35:00Z"
        }
    ]
}
```

### 3. 管理員載入所有聊天記錄
```
GET /api/admin/chat/messages
Authorization: Bearer {admin_token}

Response:
{
    "success": true,
    "data": [
        // 所有用戶的聊天記錄
    ]
}
```

### 4. 管理員發送回覆
```
POST /api/admin/chat/reply
Authorization: Bearer {admin_token}

Request Body:
{
    "user_id": "pharmacy_001",
    "message": "目前有庫存，您需要多少數量？"
}

Response:
{
    "success": true,
    "data": {
        "id": 124,
        "user_id": "pharmacy_001",
        "admin_id": "admin_001",
        "admin_name": "客服人員",
        "message": "目前有庫存，您需要多少數量？",
        "type": "admin",
        "created_at": "2025-01-13T10:35:00Z"
    }
}
```

### 5. 標記訊息為已讀/已解決
```
PUT /api/admin/chat/messages/{message_id}/status
Authorization: Bearer {admin_token}

Request Body:
{
    "status": "resolved"
}
```

## Rust 實現建議 (使用 Axum)

### 模型定義
```rust
use serde::{Deserialize, Serialize};
use sqlx::FromRow;
use chrono::{DateTime, Utc};

#[derive(Debug, Serialize, Deserialize, FromRow)]
pub struct ChatMessage {
    pub id: Option<i64>,
    pub user_id: String,
    pub user_name: String,
    pub admin_id: Option<String>,
    pub admin_name: Option<String>,
    pub message: String,
    pub message_type: String, // 'user' or 'admin'
    pub replied: bool,
    pub created_at: Option<DateTime<Utc>>,
    pub updated_at: Option<DateTime<Utc>>,
    pub status: String, // 'sent', 'read', 'resolved'
}

#[derive(Debug, Deserialize)]
pub struct SendMessageRequest {
    pub message: String,
    pub message_type: String,
}

#[derive(Debug, Deserialize)]
pub struct AdminReplyRequest {
    pub user_id: String,
    pub message: String,
}
```

### API 處理器
```rust
use axum::{
    extract::{Query, State},
    http::StatusCode,
    response::Json,
    Extension,
};
use std::collections::HashMap;

// 用戶發送訊息
pub async fn send_message(
    State(app_state): State<AppState>,
    Extension(user): Extension<User>,
    Json(payload): Json<SendMessageRequest>,
) -> Result<Json<ApiResponse<ChatMessage>>, (StatusCode, Json<ApiError>)> {
    let chat_message = ChatMessage {
        id: None,
        user_id: user.id.clone(),
        user_name: user.pharmacy_name.clone().unwrap_or(user.username.clone()),
        admin_id: None,
        admin_name: None,
        message: payload.message,
        message_type: payload.message_type,
        replied: false,
        created_at: Some(Utc::now()),
        updated_at: Some(Utc::now()),
        status: "sent".to_string(),
    };

    let result = sqlx::query!(
        r#"
        INSERT INTO chat_messages (user_id, user_name, message, type, created_at, updated_at, status)
        VALUES (?, ?, ?, ?, ?, ?, ?)
        "#,
        chat_message.user_id,
        chat_message.user_name,
        chat_message.message,
        chat_message.message_type,
        chat_message.created_at,
        chat_message.updated_at,
        chat_message.status
    )
    .execute(&app_state.db)
    .await
    .map_err(|e| {
        (
            StatusCode::INTERNAL_SERVER_ERROR,
            Json(ApiError::new("儲存訊息失敗")),
        )
    })?;

    let mut saved_message = chat_message;
    saved_message.id = Some(result.last_insert_id() as i64);

    Ok(Json(ApiResponse::success(saved_message)))
}

// 載入用戶聊天記錄
pub async fn get_user_messages(
    State(app_state): State<AppState>,
    Extension(user): Extension<User>,
    Query(params): Query<HashMap<String, String>>,
) -> Result<Json<ApiResponse<Vec<ChatMessage>>>, (StatusCode, Json<ApiError>)> {
    let user_id = params.get("user_id").unwrap_or(&user.id);

    let messages = sqlx::query_as!(
        ChatMessage,
        r#"
        SELECT id, user_id, user_name, admin_id, admin_name, message, 
               type as message_type, replied, created_at, updated_at, status
        FROM chat_messages 
        WHERE user_id = ? 
        ORDER BY created_at ASC
        "#,
        user_id
    )
    .fetch_all(&app_state.db)
    .await
    .map_err(|e| {
        (
            StatusCode::INTERNAL_SERVER_ERROR,
            Json(ApiError::new("載入聊天記錄失敗")),
        )
    })?;

    Ok(Json(ApiResponse::success(messages)))
}

// 管理員載入所有聊天記錄
pub async fn get_all_messages(
    State(app_state): State<AppState>,
    Extension(user): Extension<User>,
) -> Result<Json<ApiResponse<Vec<ChatMessage>>>, (StatusCode, Json<ApiError>)> {
    // 檢查管理員權限
    if user.role != "admin" {
        return Err((
            StatusCode::FORBIDDEN,
            Json(ApiError::new("權限不足")),
        ));
    }

    let messages = sqlx::query_as!(
        ChatMessage,
        r#"
        SELECT id, user_id, user_name, admin_id, admin_name, message, 
               type as message_type, replied, created_at, updated_at, status
        FROM chat_messages 
        ORDER BY created_at DESC
        "#
    )
    .fetch_all(&app_state.db)
    .await
    .map_err(|e| {
        (
            StatusCode::INTERNAL_SERVER_ERROR,
            Json(ApiError::new("載入聊天記錄失敗")),
        )
    })?;

    Ok(Json(ApiResponse::success(messages)))
}

// 管理員發送回覆
pub async fn send_admin_reply(
    State(app_state): State<AppState>,
    Extension(admin): Extension<User>,
    Json(payload): Json<AdminReplyRequest>,
) -> Result<Json<ApiResponse<ChatMessage>>, (StatusCode, Json<ApiError>)> {
    // 檢查管理員權限
    if admin.role != "admin" {
        return Err((
            StatusCode::FORBIDDEN,
            Json(ApiError::new("權限不足")),
        ));
    }

    let reply_message = ChatMessage {
        id: None,
        user_id: payload.user_id.clone(),
        user_name: "".to_string(), // 會在查詢時填入
        admin_id: Some(admin.id.clone()),
        admin_name: Some(admin.pharmacy_name.clone().unwrap_or(admin.username.clone())),
        message: payload.message,
        message_type: "admin".to_string(),
        replied: false,
        created_at: Some(Utc::now()),
        updated_at: Some(Utc::now()),
        status: "sent".to_string(),
    };

    // 開始事務
    let mut tx = app_state.db.begin().await.map_err(|e| {
        (
            StatusCode::INTERNAL_SERVER_ERROR,
            Json(ApiError::new("資料庫事務失敗")),
        )
    })?;

    // 插入管理員回覆
    let result = sqlx::query!(
        r#"
        INSERT INTO chat_messages (user_id, admin_id, admin_name, message, type, created_at, updated_at, status)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?)
        "#,
        payload.user_id,
        reply_message.admin_id,
        reply_message.admin_name,
        reply_message.message,
        reply_message.message_type,
        reply_message.created_at,
        reply_message.updated_at,
        reply_message.status
    )
    .execute(&mut *tx)
    .await
    .map_err(|e| {
        (
            StatusCode::INTERNAL_SERVER_ERROR,
            Json(ApiError::new("儲存回覆失敗")),
        )
    })?;

    // 標記用戶訊息為已回覆
    sqlx::query!(
        r#"
        UPDATE chat_messages 
        SET replied = TRUE, updated_at = ?
        WHERE user_id = ? AND type = 'user' AND replied = FALSE
        "#,
        Utc::now(),
        payload.user_id
    )
    .execute(&mut *tx)
    .await
    .map_err(|e| {
        (
            StatusCode::INTERNAL_SERVER_ERROR,
            Json(ApiError::new("更新訊息狀態失敗")),
        )
    })?;

    // 提交事務
    tx.commit().await.map_err(|e| {
        (
            StatusCode::INTERNAL_SERVER_ERROR,
            Json(ApiError::new("提交事務失敗")),
        )
    })?;

    let mut saved_reply = reply_message;
    saved_reply.id = Some(result.last_insert_id() as i64);

    Ok(Json(ApiResponse::success(saved_reply)))
}
```

### 路由配置
```rust
use axum::{
    routing::{get, post, put},
    Router,
};

pub fn chat_routes() -> Router<AppState> {
    Router::new()
        .route("/api/chat/messages", post(send_message))
        .route("/api/chat/messages", get(get_user_messages))
        .route("/api/admin/chat/messages", get(get_all_messages))
        .route("/api/admin/chat/reply", post(send_admin_reply))
}
```

## 使用方式

1. **實現後端 API** - 根據上述建議實現 Rust 後端 API
2. **載入前端實現** - 在前端載入 `chat-api-implementation.js`
3. **初始化功能** - 調用相應的初始化函數

這樣聊天訊息就會正確地存儲在資料庫中，管理員和用戶都能看到實時的聊天記錄。