<!DOCTYPE html>
<html lang="zh-TW">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>聊天系統 API 測試</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }

        .section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }

        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 15px;
            border-radius: 3px;
            cursor: pointer;
            margin: 5px;
        }

        button:hover {
            background: #0056b3;
        }

        textarea,
        input {
            width: 100%;
            padding: 8px;
            margin: 5px 0;
            border: 1px solid #ddd;
            border-radius: 3px;
        }

        .response {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            padding: 10px;
            margin: 10px 0;
            border-radius: 3px;
            white-space: pre-wrap;
        }

        .error {
            background: #f8d7da;
            border-color: #f5c6cb;
            color: #721c24;
        }

        .success {
            background: #d4edda;
            border-color: #c3e6cb;
            color: #155724;
        }
    </style>
</head>

<body>
    <h1>聊天系統 API 測試</h1>

    <div class="section">
        <h2>認證設定</h2>
        <input type="text" id="token" placeholder="JWT Token (如果有的話)" />
        <button onclick="setToken()">設定 Token</button>
    </div>

    <div class="section">
        <h2>發送用戶訊息</h2>
        <textarea id="userMessage" placeholder="輸入訊息內容" rows="3"></textarea>
        <button onclick="sendUserMessage()">發送訊息</button>
        <div id="sendResponse" class="response"></div>
    </div>

    <div class="section">
        <h2>獲取用戶聊天記錄</h2>
        <input type="text" id="userId" placeholder="用戶 ID (管理員可指定)" />
        <button onclick="getUserMessages()">獲取聊天記錄</button>
        <div id="messagesResponse" class="response"></div>
    </div>

    <div class="section">
        <h2>管理員功能</h2>
        <h3>獲取所有聊天記錄</h3>
        <button onclick="getAllMessages()">獲取所有聊天記錄</button>
        <div id="allMessagesResponse" class="response"></div>

        <h3>發送管理員回覆</h3>
        <input type="text" id="replyUserId" placeholder="回覆給用戶 ID" />
        <textarea id="adminReply" placeholder="管理員回覆內容" rows="3"></textarea>
        <button onclick="sendAdminReply()">發送回覆</button>
        <div id="replyResponse" class="response"></div>

        <h3>獲取聊天統計</h3>
        <button onclick="getChatStats()">獲取統計</button>
        <div id="statsResponse" class="response"></div>

        <h3>獲取對話列表</h3>
        <button onclick="getConversations()">獲取對話列表</button>
        <div id="conversationsResponse" class="response"></div>
    </div>

    <script>
        let authToken = '';
        const baseUrl = 'http://localhost:8080/api';

        function setToken() {
            authToken = document.getElementById('token').value;
            console.log('Token 已設定:', authToken ? '已設定' : '未設定');
        }

        function getHeaders() {
            const headers = {
                'Content-Type': 'application/json',
            };
            if (authToken) {
                headers['Authorization'] = `Bearer ${authToken}`;
            }
            return headers;
        }

        function displayResponse(elementId, response, isError = false) {
            const element = document.getElementById(elementId);
            element.textContent = JSON.stringify(response, null, 2);
            element.className = `response ${isError ? 'error' : 'success'}`;
        }

        async function sendUserMessage() {
            const message = document.getElementById('userMessage').value;
            if (!message.trim()) {
                alert('請輸入訊息內容');
                return;
            }

            try {
                const response = await fetch(`${baseUrl}/chat/messages`, {
                    method: 'POST',
                    headers: getHeaders(),
                    body: JSON.stringify({
                        message: message,
                        message_type: 'user'
                    })
                });

                const data = await response.json();
                displayResponse('sendResponse', data, !response.ok);

                if (response.ok) {
                    document.getElementById('userMessage').value = '';
                }
            } catch (error) {
                displayResponse('sendResponse', { error: error.message }, true);
            }
        }

        async function getUserMessages() {
            const userId = document.getElementById('userId').value;
            let url = `${baseUrl}/chat/messages`;
            if (userId) {
                url += `?user_id=${encodeURIComponent(userId)}`;
            }

            try {
                const response = await fetch(url, {
                    method: 'GET',
                    headers: getHeaders()
                });

                const data = await response.json();
                displayResponse('messagesResponse', data, !response.ok);
            } catch (error) {
                displayResponse('messagesResponse', { error: error.message }, true);
            }
        }

        async function getAllMessages() {
            try {
                const response = await fetch(`${baseUrl}/admin/chat/messages`, {
                    method: 'GET',
                    headers: getHeaders()
                });

                const data = await response.json();
                displayResponse('allMessagesResponse', data, !response.ok);
            } catch (error) {
                displayResponse('allMessagesResponse', { error: error.message }, true);
            }
        }

        async function sendAdminReply() {
            const userId = document.getElementById('replyUserId').value;
            const message = document.getElementById('adminReply').value;

            if (!userId.trim() || !message.trim()) {
                alert('請輸入用戶 ID 和回覆內容');
                return;
            }

            try {
                const response = await fetch(`${baseUrl}/admin/chat/reply`, {
                    method: 'POST',
                    headers: getHeaders(),
                    body: JSON.stringify({
                        user_id: userId,
                        message: message
                    })
                });

                const data = await response.json();
                displayResponse('replyResponse', data, !response.ok);

                if (response.ok) {
                    document.getElementById('replyUserId').value = '';
                    document.getElementById('adminReply').value = '';
                }
            } catch (error) {
                displayResponse('replyResponse', { error: error.message }, true);
            }
        }

        async function getChatStats() {
            try {
                const response = await fetch(`${baseUrl}/admin/chat/stats`, {
                    method: 'GET',
                    headers: getHeaders()
                });

                const data = await response.json();
                displayResponse('statsResponse', data, !response.ok);
            } catch (error) {
                displayResponse('statsResponse', { error: error.message }, true);
            }
        }

        async function getConversations() {
            try {
                const response = await fetch(`${baseUrl}/admin/chat/conversations`, {
                    method: 'GET',
                    headers: getHeaders()
                });

                const data = await response.json();
                displayResponse('conversationsResponse', data, !response.ok);
            } catch (error) {
                displayResponse('conversationsResponse', { error: error.message }, true);
            }
        }
    </script>
</body>

</html>