// 聊天功能 API 實現
console.log('載入聊天 API 實現...');

// 儲存聊天訊息到資料庫
async function saveChatMessage(message, type) {
    if (!currentUser) {
        console.error('用戶未登入，無法發送訊息');
        return;
    }

    const chatMessage = {
        user_id: currentUser.id || currentUser.username,
        user_name: currentUser.pharmacy_name || currentUser.username,
        message: message,
        type: type, // 'user' 或 'admin'
        created_at: new Date().toISOString(),
        status: 'sent'
    };

    try {
        console.log('發送聊天訊息到後端:', chatMessage);

        // 發送到後端 API
        const response = await apiRequest('/api/chat/messages', {
            method: 'POST',
            body: JSON.stringify(chatMessage)
        });

        if (response.success) {
            console.log('訊息已儲存到資料庫:', response.data);
            return response.data;
        } else {
            throw new Error(response.error || '儲存訊息失敗');
        }
    } catch (error) {
        console.error('儲存聊天訊息錯誤:', error);

        // 如果是網路錯誤或後端未實現，暫時存到 localStorage
        if (error.message.includes('404') || error.message.includes('Network')) {
            console.warn('後端聊天 API 未實現，暫時存到 localStorage');

            let chatHistory = JSON.parse(localStorage.getItem('chatHistory')) || [];
            chatHistory.push({ ...chatMessage, id: Date.now(), backup: true });
            localStorage.setItem('chatHistory', JSON.stringify(chatHistory));

            console.log('訊息已暫存到 localStorage');
            return { ...chatMessage, id: Date.now() };
        } else {
            showMessage('發送訊息失敗: ' + error.message, 'error');
            throw error;
        }
    }
}

// 載入聊天記錄從資料庫
async function loadChatHistory() {
    if (!currentUser) return;

    try {
        console.log('從後端載入聊天記錄...');

        // 從後端 API 載入
        const response = await apiRequest(`/api/chat/messages?user_id=${currentUser.id || currentUser.username}`);

        if (response.success) {
            console.log('從資料庫載入聊天記錄:', response.data.length, '條');
            return response.data;
        } else {
            throw new Error(response.error || '載入聊天記錄失敗');
        }
    } catch (error) {
        console.error('載入聊天記錄錯誤:', error);

        // 如果後端 API 未實現，從 localStorage 載入
        if (error.message.includes('404') || error.message.includes('Network')) {
            console.warn('後端聊天 API 未實現，從 localStorage 載入');

            const chatHistory = JSON.parse(localStorage.getItem('chatHistory')) || [];
            const userMessages = chatHistory.filter(msg =>
                msg.user_id === (currentUser.id || currentUser.username)
            );

            console.log('從 localStorage 載入聊天記錄:', userMessages.length, '條');
            return userMessages;
        } else {
            console.error('載入聊天記錄失敗:', error.message);
            return [];
        }
    }
}

// 管理員載入所有聊天記錄
async function loadAllChatMessages() {
    try {
        console.log('管理員載入所有聊天記錄...');

        // 從後端 API 載入所有聊天記錄
        const response = await apiRequest('/api/admin/chat/messages');

        if (response.success) {
            console.log('從資料庫載入所有聊天記錄:', response.data.length, '條');
            return response.data;
        } else {
            throw new Error(response.error || '載入聊天記錄失敗');
        }
    } catch (error) {
        console.error('載入所有聊天記錄錯誤:', error);

        // 如果後端 API 未實現，從 localStorage 載入
        if (error.message.includes('404') || error.message.includes('Network')) {
            console.warn('後端聊天 API 未實現，從 localStorage 載入');

            const chatHistory = JSON.parse(localStorage.getItem('chatHistory')) || [];
            console.log('從 localStorage 載入所有聊天記錄:', chatHistory.length, '條');
            return chatHistory;
        } else {
            console.error('載入聊天記錄失敗:', error.message);
            return [];
        }
    }
}

// 管理員發送回覆
async function sendAdminReply(userId, message) {
    if (!currentUser || currentUser.permissions?.role_name !== 'admin') {
        console.error('只有管理員可以發送回覆');
        return;
    }

    const replyMessage = {
        user_id: userId,
        admin_id: currentUser.id || currentUser.username,
        admin_name: currentUser.pharmacy_name || currentUser.username,
        message: message,
        type: 'admin',
        created_at: new Date().toISOString(),
        status: 'sent'
    };

    try {
        console.log('管理員發送回覆:', replyMessage);

        // 發送到後端 API
        const response = await apiRequest('/api/admin/chat/reply', {
            method: 'POST',
            body: JSON.stringify(replyMessage)
        });

        if (response.success) {
            console.log('管理員回覆已儲存到資料庫:', response.data);
            return response.data;
        } else {
            throw new Error(response.error || '發送回覆失敗');
        }
    } catch (error) {
        console.error('發送管理員回覆錯誤:', error);

        // 如果後端 API 未實現，暫時存到 localStorage
        if (error.message.includes('404') || error.message.includes('Network')) {
            console.warn('後端聊天 API 未實現，暫時存到 localStorage');

            let chatHistory = JSON.parse(localStorage.getItem('chatHistory')) || [];
            chatHistory.push({ ...replyMessage, id: Date.now(), backup: true });

            // 標記用戶訊息為已回覆
            chatHistory = chatHistory.map(msg => {
                if (msg.user_id === userId && msg.type === 'user' && !msg.replied) {
                    msg.replied = true;
                }
                return msg;
            });

            localStorage.setItem('chatHistory', JSON.stringify(chatHistory));

            console.log('管理員回覆已暫存到 localStorage');
            return { ...replyMessage, id: Date.now() };
        } else {
            showMessage('發送回覆失敗: ' + error.message, 'error');
            throw error;
        }
    }
}

// 重新定義用戶端聊天功能
function initChatFeatureWithAPI() {
    console.log('初始化基於 API 的聊天功能...');

    // 聊天表單提交事件
    const chatForm = document.getElementById('chat-form');
    if (chatForm) {
        chatForm.addEventListener('submit', async function (e) {
            e.preventDefault();

            const input = document.getElementById('chat-input');
            const message = input.value.trim();

            if (!message) {
                return;
            }

            // 顯示用戶訊息
            addChatMessage(message, 'user');
            input.value = '';
            input.style.height = 'auto';

            try {
                // 儲存到資料庫
                await saveChatMessage(message, 'user');

                // 顯示確認訊息
                setTimeout(() => {
                    addChatMessage('您的訊息已送出，客服人員會盡快回覆您！', 'system');
                }, 500);

            } catch (error) {
                // 如果儲存失敗，顯示錯誤訊息
                setTimeout(() => {
                    addChatMessage('訊息發送失敗，請稍後再試', 'system');
                }, 500);
            }
        });
    }
}

// 重新定義管理員聊天管理功能
async function loadChatManagementWithAPI() {
    console.log('載入基於 API 的聊天管理功能...');

    try {
        // 載入所有聊天記錄
        const allMessages = await loadAllChatMessages();

        // 更新統計
        updateChatStatsWithData(allMessages);

        // 載入聊天列表
        loadChatListWithData(allMessages);

        // 設置事件監聽器
        setupChatManagementEvents();

    } catch (error) {
        console.error('載入聊天管理功能失敗:', error);
        showMessage('載入聊天記錄失敗', 'error');
    }
}

// 使用數據更新統計
function updateChatStatsWithData(messages) {
    // 按用戶分組
    const userChats = {};
    messages.forEach(msg => {
        if (!userChats[msg.user_id]) {
            userChats[msg.user_id] = {
                user_id: msg.user_id,
                user_name: msg.user_name,
                messages: [],
                unread_count: 0
            };
        }
        userChats[msg.user_id].messages.push(msg);

        // 計算未讀訊息
        if (msg.type === 'user' && !msg.replied) {
            userChats[msg.user_id].unread_count++;
        }
    });

    const totalChats = Object.keys(userChats).length;
    const unreadCount = Object.values(userChats).reduce((sum, chat) => sum + chat.unread_count, 0);

    // 更新統計顯示
    const unreadCountEl = document.getElementById('unread-count');
    const totalChatsEl = document.getElementById('total-chats');

    if (unreadCountEl) unreadCountEl.textContent = unreadCount;
    if (totalChatsEl) totalChatsEl.textContent = totalChats;

    console.log('聊天統計已更新:', { totalChats, unreadCount });
}

// 使用數據載入聊天列表
function loadChatListWithData(messages, filter = 'all') {
    const chatListEl = document.getElementById('chat-list');
    if (!chatListEl) return;

    // 按用戶分組並排序
    const userChats = {};
    messages.forEach(msg => {
        if (!userChats[msg.user_id]) {
            userChats[msg.user_id] = {
                user_id: msg.user_id,
                user_name: msg.user_name,
                messages: [],
                last_message: null,
                last_message_time: null,
                unread_count: 0,
                status: 'read'
            };
        }
        userChats[msg.user_id].messages.push(msg);

        // 更新最後訊息
        if (!userChats[msg.user_id].last_message_time ||
            new Date(msg.created_at) > new Date(userChats[msg.user_id].last_message_time)) {
            userChats[msg.user_id].last_message = msg;
            userChats[msg.user_id].last_message_time = msg.created_at;
        }

        // 計算未讀訊息
        if (msg.type === 'user' && !msg.replied) {
            userChats[msg.user_id].unread_count++;
            userChats[msg.user_id].status = 'unread';
        }
    });

    // 轉換為陣列並排序
    let chatList = Object.values(userChats).sort((a, b) =>
        new Date(b.last_message_time) - new Date(a.last_message_time)
    );

    // 應用篩選
    if (filter === 'unread') {
        chatList = chatList.filter(chat => chat.unread_count > 0);
    } else if (filter === 'replied') {
        chatList = chatList.filter(chat => chat.unread_count === 0);
    }

    // 渲染聊天列表
    if (chatList.length === 0) {
        chatListEl.innerHTML = `
      <div class="no-chats">
        <div class="no-chats-icon">💬</div>
        <h4>暫無留言</h4>
        <p>目前沒有用戶留言</p>
      </div>
    `;
        return;
    }

    chatListEl.innerHTML = chatList.map(chat => `
    <div class="chat-item ${chat.status}" onclick="openChatConversation('${chat.user_id}')">
      <div class="chat-avatar">👤</div>
      <div class="chat-info">
        <div class="chat-user-name">${chat.user_name}</div>
        <div class="chat-last-message">${chat.last_message ? chat.last_message.message : '暫無訊息'}</div>
      </div>
      <div class="chat-meta">
        <div class="chat-time">${chat.last_message_time ? new Date(chat.last_message_time).toLocaleString('zh-TW', {
        month: 'short',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
    }) : ''}</div>
        <div class="chat-status ${chat.status}">
          ${chat.status === 'unread' ? `未讀 (${chat.unread_count})` : '已讀'}
        </div>
      </div>
    </div>
  `).join('');

    console.log('聊天列表已渲染:', chatList.length, '個對話');
}

// 導出函數供全局使用
window.saveChatMessage = saveChatMessage;
window.loadChatHistory = loadChatHistory;
window.loadAllChatMessages = loadAllChatMessages;
window.sendAdminReply = sendAdminReply;
window.initChatFeatureWithAPI = initChatFeatureWithAPI;
window.loadChatManagementWithAPI = loadChatManagementWithAPI;

console.log('聊天 API 實現已載入');
console.log('可用函數:');
console.log('- initChatFeatureWithAPI() - 初始化用戶端聊天功能');
console.log('- loadChatManagementWithAPI() - 載入管理員聊天管理');
console.log('- loadAllChatMessages() - 載入所有聊天記錄');
console.log('- sendAdminReply(userId, message) - 管理員發送回覆');