<!DOCTYPE html>
<html lang="zh-TW">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>配置測試</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
        }

        .config-item {
            margin: 10px 0;
            padding: 10px;
            background: #f5f5f5;
            border-radius: 5px;
        }

        .success {
            background: #d4edda;
            color: #155724;
        }

        .error {
            background: #f8d7da;
            color: #721c24;
        }
    </style>
</head>

<body>
    <h1>藥房管理系統 - 配置測試</h1>

    <div id="config-info">
        <div class="config-item">
            <strong>當前域名：</strong> <span id="hostname"></span>
        </div>
        <div class="config-item">
            <strong>當前協議：</strong> <span id="protocol"></span>
        </div>
        <div class="config-item">
            <strong>完整 URL：</strong> <span id="full-url"></span>
        </div>
        <div class="config-item">
            <strong>API_BASE：</strong> <span id="api-base"></span>
        </div>
        <div class="config-item">
            <strong>DEBUG 模式：</strong> <span id="debug-mode"></span>
        </div>
        <div class="config-item">
            <strong>環境判斷：</strong> <span id="environment"></span>
        </div>
    </div>

    <div id="test-results">
        <h2>測試結果</h2>
        <div id="config-test" class="config-item">測試中...</div>
        <div id="api-test" class="config-item">API 連線測試中...</div>
    </div>

    <script src="js/config.js"></script>
    <script>
        // 顯示配置信息
        document.getElementById('hostname').textContent = window.location.hostname;
        document.getElementById('protocol').textContent = window.location.protocol;
        document.getElementById('full-url').textContent = window.location.href;

        // 等待配置載入
        setTimeout(() => {
            const config = window.CONFIG;
            const apiBase = config ? config.API_BASE : '配置未載入';
            const debugMode = config ? config.DEBUG : '未知';

            document.getElementById('api-base').textContent = apiBase;
            document.getElementById('debug-mode').textContent = debugMode;

            // 環境判斷
            const hostname = window.location.hostname;
            let environment = '未知';
            if (hostname === 'localhost' || hostname === '127.0.0.1') {
                environment = '開發環境';
            } else if (hostname === 'order.53617503.xyz') {
                environment = '生產環境';
            }
            document.getElementById('environment').textContent = environment;

            // 配置測試
            const configTest = document.getElementById('config-test');
            if (config && config.API_BASE) {
                configTest.textContent = '✅ 配置載入成功';
                configTest.className = 'config-item success';
            } else {
                configTest.textContent = '❌ 配置載入失敗';
                configTest.className = 'config-item error';
            }

            // API 連線測試
            const apiTest = document.getElementById('api-test');
            if (apiBase) {
                fetch(apiBase)
                    .then(response => {
                        if (response.ok) {
                            apiTest.textContent = '✅ API 連線成功';
                            apiTest.className = 'config-item success';
                        } else {
                            apiTest.textContent = `⚠️ API 回應狀態: ${response.status}`;
                            apiTest.className = 'config-item';
                        }
                    })
                    .catch(error => {
                        apiTest.textContent = `❌ API 連線失敗: ${error.message}`;
                        apiTest.className = 'config-item error';
                    });
            } else {
                apiTest.textContent = '❌ 無法測試 API（配置未載入）';
                apiTest.className = 'config-item error';
            }
        }, 100);
    </script>
</body>

</html>