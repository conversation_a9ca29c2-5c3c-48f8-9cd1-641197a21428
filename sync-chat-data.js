// 聊天數據同步腳本
console.log('聊天數據同步腳本已載入');

// 從藥局端複製數據到管理員端
function syncChatFromPharmacy() {
    console.log('開始從藥局端同步數據...');

    // 檢查當前 localStorage 中的聊天記錄
    const currentChatHistory = JSON.parse(localStorage.getItem('chatHistory') || '[]');
    console.log('當前聊天記錄數量:', currentChatHistory.length);

    if (currentChatHistory.length > 0) {
        console.log('找到聊天記錄:');
        currentChatHistory.forEach((msg, index) => {
            console.log(`${index + 1}. [${msg.type}] ${msg.user_name}: ${msg.message}`);
        });

        // 刷新管理員介面
        refreshAdminInterface();
        return true;
    } else {
        console.log('沒有找到聊天記錄');
        return false;
    }
}

// 刷新管理員介面
function refreshAdminInterface() {
    console.log('刷新管理員介面...');

    // 檢查是否在管理員頁面
    const isAdminPage = document.getElementById('chat-management-admin') !== null;
    console.log('是否在管理員頁面:', isAdminPage);

    if (isAdminPage) {
        // 刷新統計
        if (typeof updateChatStats === 'function') {
            updateChatStats();
            console.log('統計已更新');
        }

        // 刷新聊天列表
        if (typeof loadChatList === 'function') {
            loadChatList();
            console.log('聊天列表已更新');
        }

        console.log('管理員介面刷新完成');
    } else {
        console.log('不在管理員頁面，無需刷新');
    }
}

// 監聽 localStorage 變化（跨標籤頁同步）
window.addEventListener('storage', function (e) {
    if (e.key === 'chatHistory') {
        console.log('檢測到聊天記錄變化，自動刷新...');
        setTimeout(() => {
            refreshAdminInterface();
        }, 500);
    }
});

// 定期檢查數據變化
let lastChatCount = 0;
function periodicCheck() {
    const currentChatHistory = JSON.parse(localStorage.getItem('chatHistory') || '[]');
    if (currentChatHistory.length !== lastChatCount) {
        console.log(`聊天記錄數量變化: ${lastChatCount} -> ${currentChatHistory.length}`);
        lastChatCount = currentChatHistory.length;
        refreshAdminInterface();
    }
}

// 每5秒檢查一次
setInterval(periodicCheck, 5000);

// 手動同步函數
window.manualSync = function () {
    console.log('執行手動同步...');
    syncChatFromPharmacy();
};

// 顯示當前數據
window.showCurrentData = function () {
    const chatHistory = JSON.parse(localStorage.getItem('chatHistory') || '[]');
    console.log('=== 當前聊天數據 ===');
    console.log('總數量:', chatHistory.length);
    chatHistory.forEach((msg, index) => {
        console.log(`${index + 1}. [${msg.created_at}] ${msg.user_name} (${msg.type}): ${msg.message}`);
    });
    console.log('==================');
};

// 清除所有數據
window.clearAllChatData = function () {
    if (confirm('確定要清除所有聊天數據嗎？')) {
        localStorage.removeItem('chatHistory');
        refreshAdminInterface();
        console.log('所有聊天數據已清除');
    }
};

// 初始化
setTimeout(() => {
    syncChatFromPharmacy();
    lastChatCount = JSON.parse(localStorage.getItem('chatHistory') || '[]').length;
}, 1000);

console.log('可用函數:');
console.log('- manualSync() - 手動同步數據');
console.log('- showCurrentData() - 顯示當前數據');
console.log('- clearAllChatData() - 清除所有數據');
console.log('- refreshAdminInterface() - 刷新管理員介面');