<!DOCTYPE html>
<html lang="zh-TW">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>產品載入調試</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
        }

        .debug-info {
            background: #f5f5f5;
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
        }

        .error {
            color: red;
        }

        .success {
            color: green;
        }

        .warning {
            color: orange;
        }
    </style>
</head>

<body>
    <h1>產品載入調試頁面</h1>
    <div id="debug-output"></div>
    <button onclick="testProductLoad()">測試產品載入</button>
    <button onclick="testDirectAPI()">直接測試 API</button>

    <script src="web/js/config.js"></script>
    <script>
        const debugOutput = document.getElementById('debug-output');

        function log(message, type = 'info') {
            const div = document.createElement('div');
            div.className = `debug-info ${type}`;
            div.innerHTML = `[${new Date().toLocaleTimeString()}] ${message}`;
            debugOutput.appendChild(div);
        }

        // 測試配置
        log('配置檢查: ' + (window.CONFIG ? '✅ 成功' : '❌ 失敗'));
        if (window.CONFIG) {
            log('API_BASE: ' + window.CONFIG.API_BASE);
        }

        // 測試認證狀態
        const authToken = localStorage.getItem("authToken");
        log('認證狀態: ' + (authToken ? '✅ 已登入' : '❌ 未登入'));

        async function testDirectAPI() {
            try {
                log('開始直接 API 測試...', 'info');
                const response = await fetch('http://localhost:8080/api/products');
                const data = await response.json();

                if (data.success) {
                    log(`✅ API 測試成功，獲得 ${data.data.length} 個產品`, 'success');
                } else {
                    log('❌ API 返回錯誤: ' + data.error, 'error');
                }
            } catch (error) {
                log('❌ API 測試失敗: ' + error.message, 'error');
            }
        }

        async function testProductLoad() {
            try {
                log('開始產品載入測試...', 'info');

                // 檢查必要的函數是否存在
                if (typeof apiRequest === 'undefined') {
                    log('❌ apiRequest 函數不存在', 'error');
                    return;
                }

                // 模擬 loadProducts 函數的邏輯
                const params = new URLSearchParams();
                params.append("page", "1");
                params.append("limit", "10");
                const url = `/api/products?${params}`;

                log('請求 URL: ' + url, 'info');

                // 這裡需要先載入 app.js 才能使用 apiRequest
                log('❌ 需要先載入完整的 app.js 文件', 'warning');

            } catch (error) {
                log('❌ 產品載入測試失敗: ' + error.message, 'error');
            }
        }
    </script>
</body>

</html>