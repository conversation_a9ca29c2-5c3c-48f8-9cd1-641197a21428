// 產品載入修復腳本
console.log('開始產品載入修復...');

// 檢查必要的元素是否存在
function checkElements() {
    const gridBody = document.getElementById('products-grid-body');
    const searchBtn = document.getElementById('search-btn');
    const productSearch = document.getElementById('product-search');

    console.log('DOM 元素檢查:');
    console.log('- products-grid-body:', !!gridBody);
    console.log('- search-btn:', !!searchBtn);
    console.log('- product-search:', !!productSearch);

    return { gridBody, searchBtn, productSearch };
}

// 簡化的產品載入函數
async function simpleLoadProducts() {
    console.log('開始簡化產品載入...');

    const { gridBody } = checkElements();
    if (!gridBody) {
        console.error('找不到 products-grid-body 元素');
        return;
    }

    try {
        // 顯示載入中
        gridBody.innerHTML = '<div style="text-align: center; padding: 40px;">載入中...</div>';

        // 直接調用 API
        const response = await fetch('http://localhost:8080/api/products?page=1&limit=10');
        const data = await response.json();

        if (data.success && data.data) {
            console.log(`成功載入 ${data.data.length} 個產品`);

            // 簡化的產品顯示
            const productsHtml = data.data.map(product => `
                <div style="border: 1px solid #ddd; margin: 10px; padding: 15px; border-radius: 5px;">
                    <h4>${product.name || 'N/A'}</h4>
                    <p><strong>健保代碼:</strong> ${product.nhi_code || 'N/A'}</p>
                    <p><strong>成分:</strong> ${product.ingredients || 'N/A'}</p>
                    <p><strong>規格:</strong> ${product.dosage_form || 'N/A'}</p>
                    <p><strong>售價:</strong> NT$ ${product.selling_price || '0'}</p>
                    <p><strong>庫存:</strong> ${product.stock_quantity || 0}</p>
                    <p><strong>製造商:</strong> ${product.manufacturer || 'N/A'}</p>
                </div>
            `).join('');

            gridBody.innerHTML = productsHtml;

        } else {
            throw new Error(data.error || '載入產品失敗');
        }

    } catch (error) {
        console.error('產品載入錯誤:', error);
        gridBody.innerHTML = `<div style="text-align: center; padding: 40px; color: red;">載入失敗: ${error.message}</div>`;
    }
}

// 等待 DOM 載入完成後執行
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', function () {
        setTimeout(() => {
            checkElements();
            simpleLoadProducts();
        }, 1000);
    });
} else {
    setTimeout(() => {
        checkElements();
        simpleLoadProducts();
    }, 1000);
}

// 導出函數供手動調用
window.simpleLoadProducts = simpleLoadProducts;
window.checkElements = checkElements;

console.log('修復腳本載入完成，可以手動調用 simpleLoadProducts() 來載入產品');