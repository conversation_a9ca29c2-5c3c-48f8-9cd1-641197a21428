<!DOCTYPE html>
<html lang="zh-TW">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>產品載入修復測試</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
        }

        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }

        button {
            padding: 10px 15px;
            margin: 5px;
            background: #007bff;
            color: white;
            border: none;
            border-radius: 3px;
            cursor: pointer;
        }

        button:hover {
            background: #0056b3;
        }

        .products-grid-body {
            border: 1px solid #ccc;
            min-height: 200px;
            padding: 10px;
        }
    </style>
</head>

<body>
    <h1>產品載入修復測試</h1>

    <div class="test-section">
        <h3>測試控制</h3>
        <button onclick="window.checkElements()">檢查 DOM 元素</button>
        <button onclick="window.simpleLoadProducts()">載入產品</button>
        <button onclick="testDirectAPI()">直接測試 API</button>
        <button onclick="clearProducts()">清除產品</button>
    </div>

    <div class="test-section">
        <h3>產品顯示區域</h3>
        <div id="products-grid-body" class="products-grid-body">
            <div style="text-align: center; padding: 40px; color: #999;">
                點擊"載入產品"按鈕來載入產品列表
            </div>
        </div>
    </div>

    <div class="test-section">
        <h3>調試信息</h3>
        <div id="debug-info" style="background: #f8f9fa; padding: 10px; font-family: monospace; white-space: pre-wrap;">
        </div>
    </div>

    <script src="fix-products.js"></script>
    <script>
        const debugInfo = document.getElementById('debug-info');

        // 重寫 console.log 來顯示調試信息
        const originalLog = console.log;
        console.log = function (...args) {
            originalLog.apply(console, args);
            debugInfo.textContent += args.join(' ') + '\n';
            debugInfo.scrollTop = debugInfo.scrollHeight;
        };

        const originalError = console.error;
        console.error = function (...args) {
            originalError.apply(console, args);
            debugInfo.textContent += 'ERROR: ' + args.join(' ') + '\n';
            debugInfo.scrollTop = debugInfo.scrollHeight;
        };

        async function testDirectAPI() {
            try {
                console.log('開始直接 API 測試...');
                const response = await fetch('http://localhost:8080/api/products?page=1&limit=3');
                const data = await response.json();

                if (data.success) {
                    console.log(`API 測試成功，獲得 ${data.data.length} 個產品`);
                    console.log('第一個產品:', data.data[0]?.name);
                } else {
                    console.error('API 返回錯誤:', data.error);
                }
            } catch (error) {
                console.error('API 測試失敗:', error.message);
            }
        }

        function clearProducts() {
            const gridBody = document.getElementById('products-grid-body');
            if (gridBody) {
                gridBody.innerHTML = '<div style="text-align: center; padding: 40px; color: #999;">產品已清除</div>';
                console.log('產品顯示區域已清除');
            }
        }

        console.log('測試頁面載入完成');
    </script>
</body>

</html>