<!DOCTYPE html>
<html lang="zh-TW">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>聊天功能測試</title>
    <link rel="stylesheet" href="web/css/style.css">
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }

        .test-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 12px;
            padding: 20px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }

        h1 {
            text-align: center;
            color: #333;
            margin-bottom: 30px;
        }
    </style>
</head>

<body>
    <div class="test-container">
        <h1>💬 聊天功能測試</h1>

        <div class="card">
            <div class="contact-header">
                <h3>💬 與我聯絡</h3>
                <p class="contact-subtitle">有任何問題或建議，歡迎與我們即時對話</p>
            </div>

            <div class="chat-container">
                <!-- 聊天訊息區域 -->
                <div id="chat-messages" class="chat-messages">
                    <div class="system-message">
                        <div class="message-content">
                            <div class="avatar">🤖</div>
                            <div class="message-bubble system">
                                <p>您好！我是客服助理，有什麼可以幫助您的嗎？</p>
                                <span class="message-time">剛剛</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 輸入區域 -->
                <div class="chat-input-container">
                    <form id="chat-form" class="chat-form">
                        <div class="input-group">
                            <textarea id="chat-input" placeholder="輸入您的訊息..." rows="1" required></textarea>
                            <button type="submit" class="send-btn">
                                <span>發送</span>
                                <span class="send-icon">📤</span>
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <div style="margin-top: 20px; padding: 15px; background: #f8f9fa; border-radius: 8px;">
            <h4>測試建議：</h4>
            <p>試試輸入以下關鍵字來測試自動回覆：</p>
            <ul>
                <li><strong>價格</strong> - 詢問產品價格</li>
                <li><strong>訂單</strong> - 訂單相關問題</li>
                <li><strong>庫存</strong> - 庫存查詢</li>
                <li><strong>配送</strong> - 配送相關</li>
                <li><strong>退貨</strong> - 退換貨政策</li>
                <li><strong>會員</strong> - 會員註冊</li>
                <li><strong>你好</strong> - 打招呼</li>
            </ul>
        </div>
    </div>

    <script>
        // 添加聊天訊息
        function addChatMessage(message, type) {
            const chatMessages = document.getElementById('chat-messages');
            if (!chatMessages) return;

            const messageDiv = document.createElement('div');
            messageDiv.className = `message-content ${type}-message`;

            const now = new Date();
            const timeStr = now.toLocaleTimeString('zh-TW', {
                hour: '2-digit',
                minute: '2-digit'
            });

            let avatar, bubbleClass;
            if (type === 'user') {
                avatar = '👤';
                bubbleClass = 'user';
            } else if (type === 'admin') {
                avatar = '👨‍💼';
                bubbleClass = 'admin';
            } else {
                avatar = '🤖';
                bubbleClass = 'system';
            }

            messageDiv.innerHTML = `
                <div class="avatar">${avatar}</div>
                <div class="message-bubble ${bubbleClass}">
                    <p>${message}</p>
                    <span class="message-time">${timeStr}</span>
                </div>
            `;

            chatMessages.appendChild(messageDiv);
            chatMessages.scrollTop = chatMessages.scrollHeight;
        }

        // 顯示打字指示器
        function showTypingIndicator() {
            const chatMessages = document.getElementById('chat-messages');
            if (!chatMessages) return;

            const typingDiv = document.createElement('div');
            typingDiv.className = 'message-content typing-message';
            typingDiv.id = 'typing-indicator';

            typingDiv.innerHTML = `
                <div class="avatar">👨‍💼</div>
                <div class="typing-indicator">
                    <div class="typing-dots">
                        <div class="typing-dot"></div>
                        <div class="typing-dot"></div>
                        <div class="typing-dot"></div>
                    </div>
                </div>
            `;

            chatMessages.appendChild(typingDiv);
            chatMessages.scrollTop = chatMessages.scrollHeight;
        }

        // 隱藏打字指示器
        function hideTypingIndicator() {
            const typingIndicator = document.getElementById('typing-indicator');
            if (typingIndicator) {
                typingIndicator.remove();
            }
        }

        // 生成自動回覆
        function generateAutoReply(userMessage) {
            const message = userMessage.toLowerCase();

            if (message.includes('價格') || message.includes('多少錢') || message.includes('費用')) {
                return '關於價格問題，我們的產品都有標示建議售價，實際價格可能因地區而異。您可以在產品頁面查看詳細資訊，或聯繫我們獲取最新報價。';
            }

            if (message.includes('訂單') || message.includes('下單') || message.includes('購買')) {
                return '關於訂單問題，您可以在「我的訂單」頁面查看訂單狀態。如需協助下單，請告訴我您需要的產品，我會為您提供詳細說明。';
            }

            if (message.includes('庫存') || message.includes('有沒有') || message.includes('現貨')) {
                return '關於庫存查詢，您可以在產品頁面查看即時庫存狀況。如果顯示「缺貨」，我們會盡快補貨，建議您可以先加入購物車，有貨時我們會通知您。';
            }

            if (message.includes('配送') || message.includes('運送') || message.includes('物流')) {
                return '我們提供多種配送方式，一般訂單會在1-3個工作天內出貨。急件可選擇當日配送服務（需額外收費）。詳細配送資訊請參考訂單頁面說明。';
            }

            if (message.includes('退貨') || message.includes('退換') || message.includes('不滿意')) {
                return '我們提供7天鑑賞期，如商品有品質問題或不符需求，請保持商品完整包裝，聯繫客服辦理退換貨。藥品類商品基於安全考量，恕不接受退貨。';
            }

            if (message.includes('會員') || message.includes('註冊') || message.includes('帳號')) {
                return '註冊會員可享有更多優惠和便利服務！註冊後需要管理員審核，審核通過後即可開始使用。如有註冊問題，請提供您的註冊資訊，我會協助處理。';
            }

            if (message.includes('謝謝') || message.includes('感謝')) {
                return '不客氣！很高興能為您服務。如果還有其他問題，隨時歡迎詢問！😊';
            }

            if (message.includes('你好') || message.includes('哈囉') || message.includes('嗨')) {
                return '您好！歡迎來到台南社區藥局藥品供應平台，我是客服助理。請問有什麼可以為您服務的嗎？';
            }

            const defaultReplies = [
                '感謝您的詢問！我已收到您的訊息，會盡快為您處理。如果是緊急問題，建議您直接撥打客服專線。',
                '您的問題我已記錄下來，相關同事會在24小時內回覆您。期間如有其他問題，歡迎隨時詢問！',
                '謝謝您的留言！我會將您的問題轉達給相關部門，並盡快為您提供詳細回覆。'
            ];

            return defaultReplies[Math.floor(Math.random() * defaultReplies.length)];
        }

        // 初始化聊天功能
        document.addEventListener('DOMContentLoaded', function () {
            const chatForm = document.getElementById('chat-form');
            if (chatForm) {
                chatForm.addEventListener('submit', function (e) {
                    e.preventDefault();

                    const input = document.getElementById('chat-input');
                    const message = input.value.trim();

                    if (!message) {
                        return;
                    }

                    // 發送用戶訊息
                    addChatMessage(message, 'user');
                    input.value = '';
                    input.style.height = 'auto';

                    // 模擬客服回覆
                    setTimeout(() => {
                        showTypingIndicator();
                        setTimeout(() => {
                            hideTypingIndicator();
                            const reply = generateAutoReply(message);
                            addChatMessage(reply, 'admin');
                        }, 1500);
                    }, 500);
                });
            }

            // 自動調整輸入框高度
            const chatInput = document.getElementById('chat-input');
            if (chatInput) {
                chatInput.addEventListener('input', function () {
                    this.style.height = 'auto';
                    this.style.height = Math.min(this.scrollHeight, 100) + 'px';
                });

                // 按 Enter 發送，Shift+Enter 換行
                chatInput.addEventListener('keydown', function (e) {
                    if (e.key === 'Enter' && !e.shiftKey) {
                        e.preventDefault();
                        chatForm.dispatchEvent(new Event('submit'));
                    }
                });
            }
        });
    </script>
</body>

</html>