<!DOCTYPE html>
<html lang="zh-TW">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>訊息功能測試</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
        }

        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }

        button {
            padding: 10px 15px;
            margin: 5px;
            background: #007bff;
            color: white;
            border: none;
            border-radius: 3px;
            cursor: pointer;
        }

        button:hover {
            background: #0056b3;
        }

        .promotions-view-content {
            border: 1px solid #ccc;
            min-height: 200px;
            padding: 10px;
            background: #f9f9f9;
        }

        .promotion-message-card {
            border: 1px solid #ddd;
            margin: 10px 0;
            padding: 15px;
            border-radius: 5px;
            background: white;
        }

        .message-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
        }

        .message-type-info {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .message-icon {
            font-size: 20px;
        }

        .message-type-label {
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
        }

        .message-title {
            margin: 10px 0;
            color: #333;
        }

        .message-text {
            color: #666;
            line-height: 1.5;
        }

        .no-messages {
            text-align: center;
            padding: 40px;
            color: #999;
        }
    </style>
</head>

<body>
    <h1>訊息功能測試</h1>

    <div class="test-section">
        <h3>測試控制</h3>
        <button onclick="checkMessageElements()">檢查訊息元素</button>
        <button onclick="testLoadMessages()">載入訊息</button>
        <button onclick="createTestMessages()">創建測試訊息</button>
        <button onclick="clearMessages()">清除訊息</button>
    </div>

    <div class="test-section">
        <h3>訊息顯示區域</h3>
        <div id="promotions-view-content" class="promotions-view-content">
            <div class="no-messages">
                <div>📭</div>
                <h3>等待載入訊息...</h3>
                <p>點擊"載入訊息"按鈕來測試訊息功能</p>
            </div>
        </div>
    </div>

    <div class="test-section">
        <h3>調試信息</h3>
        <div id="debug-info"
            style="background: #f8f9fa; padding: 10px; font-family: monospace; white-space: pre-wrap; max-height: 300px; overflow-y: auto;">
        </div>
    </div>

    <script>
        const debugInfo = document.getElementById('debug-info');

        // 重寫 console.log 來顯示調試信息
        const originalLog = console.log;
        console.log = function (...args) {
            originalLog.apply(console, args);
            debugInfo.textContent += args.join(' ') + '\n';
            debugInfo.scrollTop = debugInfo.scrollHeight;
        };

        const originalError = console.error;
        console.error = function (...args) {
            originalError.apply(console, args);
            debugInfo.textContent += 'ERROR: ' + args.join(' ') + '\n';
            debugInfo.scrollTop = debugInfo.scrollHeight;
        };

        function checkMessageElements() {
            const container = document.getElementById('promotions-view-content');
            console.log('訊息容器檢查:');
            console.log('- promotions-view-content 存在:', !!container);
            if (container) {
                console.log('- 容器類名:', container.className);
                console.log('- 容器內容長度:', container.innerHTML.length);
            }
        }

        function createTestMessages() {
            const testMessages = [
                {
                    id: 1,
                    sent_at: new Date(Date.now() - 86400000).toISOString(),
                    type: 'announcement',
                    title: '系統維護通知',
                    content: '系統將於本週末進行維護，維護期間可能無法正常使用，敬請見諒。',
                    target_audience: 'all',
                    send_methods: ['web'],
                    status: 'sent'
                },
                {
                    id: 2,
                    sent_at: new Date().toISOString(),
                    type: 'promotion',
                    title: '新品上市優惠',
                    content: '新品上市，全館商品 8 折優惠，歡迎選購！',
                    target_audience: 'all',
                    send_methods: ['web'],
                    status: 'sent'
                },
                {
                    id: 3,
                    sent_at: new Date(Date.now() - 3600000).toISOString(),
                    type: 'urgent',
                    title: '緊急通知',
                    content: '由於系統異常，部分功能暫時無法使用，我們正在緊急修復中。',
                    target_audience: 'all',
                    send_methods: ['web', 'email'],
                    status: 'sent'
                }
            ];

            localStorage.setItem("messageHistory", JSON.stringify(testMessages));
            console.log('已創建測試訊息:', testMessages.length, '條');
            return testMessages;
        }

        function renderPromotionMessages(messages) {
            const container = document.getElementById('promotions-view-content');
            if (!container) {
                console.error('找不到促銷訊息容器');
                return;
            }

            if (messages.length === 0) {
                container.innerHTML = `
                    <div class="no-messages">
                        <div>📭</div>
                        <h3>暫無訊息</h3>
                        <p>目前沒有新的促銷訊息或通知</p>
                    </div>
                `;
                return;
            }

            const typeLabels = {
                'promotion': '促銷活動',
                'announcement': '系統公告',
                'maintenance': '維護通知',
                'urgent': '緊急通知'
            };

            const typeIcons = {
                'promotion': '🎉',
                'announcement': '📢',
                'maintenance': '🔧',
                'urgent': '⚠️'
            };

            const typeColors = {
                'promotion': '#e8f5e8',
                'announcement': '#e3f2fd',
                'maintenance': '#fff3e0',
                'urgent': '#ffebee'
            };

            container.innerHTML = messages.map(message => `
                <div class="promotion-message-card" data-type="${message.type}">
                    <div class="message-header">
                        <div class="message-type-info">
                            <span class="message-icon">${typeIcons[message.type]}</span>
                            <span class="message-type-label" style="background-color: ${typeColors[message.type]}">
                                ${typeLabels[message.type]}
                            </span>
                        </div>
                        <div class="message-date">
                            ${new Date(message.sent_at).toLocaleDateString('zh-TW')}
                        </div>
                    </div>
                    
                    <div class="message-content">
                        <h3 class="message-title">${message.title}</h3>
                        <div class="message-text">
                            ${message.content.replace(/\n/g, '<br>')}
                        </div>
                    </div>
                </div>
            `).join('');

            console.log('已渲染', messages.length, '條訊息');
        }

        function testLoadMessages() {
            console.log('開始載入訊息測試...');

            // 從 localStorage 獲取訊息記錄
            let messageHistory = JSON.parse(localStorage.getItem("messageHistory")) || [];
            console.log('從 localStorage 讀取的訊息記錄:', messageHistory.length, '條');

            // 如果沒有訊息記錄，創建測試訊息
            if (messageHistory.length === 0) {
                console.log('沒有找到訊息記錄，創建測試訊息');
                messageHistory = createTestMessages();
            }

            // 過濾出已發送的訊息，按時間排序（最新的在前）
            const sentMessages = messageHistory
                .filter(msg => msg.status === 'sent')
                .sort((a, b) => new Date(b.sent_at) - new Date(a.sent_at));

            console.log('過濾後的已發送訊息數量:', sentMessages.length);

            renderPromotionMessages(sentMessages);
        }

        function clearMessages() {
            const container = document.getElementById('promotions-view-content');
            if (container) {
                container.innerHTML = `
                    <div class="no-messages">
                        <div>📭</div>
                        <h3>訊息已清除</h3>
                        <p>所有訊息已被清除</p>
                    </div>
                `;
                console.log('訊息顯示區域已清除');
            }
            localStorage.removeItem("messageHistory");
            console.log('localStorage 中的訊息記錄已清除');
        }

        console.log('訊息測試頁面載入完成');
    </script>
</body>

</html>