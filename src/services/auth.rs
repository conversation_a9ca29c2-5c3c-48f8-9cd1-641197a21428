use crate::{
    models::User,
    repositories::{
        user::{UserRepository, PostgresUserRepository, CreateUser},
        role::{RoleRepository, PostgresRoleRepository},
        base::BaseRepository,
    },
    database::DbPool,
    error::AppError,
    config::Config,
};
use async_trait::async_trait;
use bcrypt::{hash, verify, DEFAULT_COST};
use jsonwebtoken::{encode, decode, Header, Validation, EncodingKey, DecodingKey, Algorithm};
use serde::{Deserialize, Serialize};
use chrono::{Duration, Utc};
use std::collections::HashMap;
use std::sync::{Arc, Mutex};

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Claims {
    pub sub: String, // user_id
    pub exp: i64,    // expiration time
    pub iat: i64,    // issued at
    pub username: String,
    pub user_id: i64,
    pub role_id: Option<i64>,
}

#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct LoginRequest {
    pub username: String,
    pub password: String,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct RegisterRequest {
    pub username: String,
    pub email: String,
    pub password: String,
    pub pharmacy_name: String,
    pub contact_person: String,
    pub phone: String,
    pub mobile: String,
    pub institution_code: String,
    pub address: String,
    pub line_user_id: Option<String>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct LoginResponse {
    pub token: String,
    pub user: User,
    pub expires_in: i64,
    pub permissions: Option<crate::models::UserPermissions>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct RefreshTokenRequest {
    pub token: String,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SessionInfo {
    pub token_id: String,
    pub user_id: i64,
    pub created_at: chrono::DateTime<chrono::Utc>,
    pub expires_at: chrono::DateTime<chrono::Utc>,
    pub is_active: bool,
}

#[async_trait]
pub trait AuthService: Send + Sync {
    async fn register(&self, request: RegisterRequest) -> Result<User, AppError>;
    async fn login(&self, request: LoginRequest) -> Result<LoginResponse, AppError>;
    async fn validate_token(&self, token: &str) -> Result<Claims, AppError>;
    async fn refresh_token(&self, request: RefreshTokenRequest) -> Result<LoginResponse, AppError>;
    async fn change_password(&self, user_id: i64, old_password: &str, new_password: &str) -> Result<(), AppError>;
    async fn logout(&self, token: &str) -> Result<(), AppError>;
    async fn get_user_sessions(&self, user_id: i64) -> Result<Vec<SessionInfo>, AppError>;
    async fn get_user_by_id(&self, user_id: i64) -> Result<Option<User>, AppError>;
    async fn update_user_profile(&self, user_id: i64, request: crate::api::auth::UpdateProfileRequest) -> Result<User, AppError>;
    async fn revoke_session(&self, user_id: i64, session_id: &str) -> Result<(), AppError>;
}

pub struct AuthServiceImpl {
    user_repo: PostgresUserRepository,
    role_repo: PostgresRoleRepository,
    config: Config,
    // 簡單的記憶體會話儲存，實際應用中應該使用 Redis 或資料庫
    active_sessions: Arc<Mutex<HashMap<String, SessionInfo>>>,
}

impl AuthServiceImpl {
    pub fn new(pool: DbPool, config: Config) -> Self {
        Self {
            user_repo: PostgresUserRepository::new(pool.clone()),
            role_repo: PostgresRoleRepository::new(pool),
            config,
            active_sessions: Arc::new(Mutex::new(HashMap::new())),
        }
    }

    fn hash_password(&self, password: &str) -> Result<String, AppError> {
        hash(password, DEFAULT_COST)
            .map_err(|e| AppError::Internal(format!("Password hashing failed: {}", e)))
    }

    fn verify_password(&self, password: &str, hash: &str) -> Result<bool, AppError> {
        verify(password, hash)
            .map_err(|e| AppError::Internal(format!("Password verification failed: {}", e)))
    }

    fn generate_token(&self, user: &User) -> Result<String, AppError> {
        let now = Utc::now();
        let expires_at = now + Duration::hours(24);

        let claims = Claims {
            sub: user.id.to_string(),
            exp: expires_at.timestamp(),
            iat: now.timestamp(),
            username: user.username.clone(),
            user_id: user.id,
            role_id: Some(user.role_id),
        };

        let token = encode(
            &Header::default(),
            &claims,
            &EncodingKey::from_secret(self.config.jwt_secret.as_ref()),
        )
        .map_err(|e| AppError::Internal(format!("Token generation failed: {}", e)))?;

        // 儲存會話資訊
        let session_info = SessionInfo {
            token_id: token.clone(),
            user_id: user.id,
            created_at: now,
            expires_at,
            is_active: true,
        };

        if let Ok(mut sessions) = self.active_sessions.lock() {
            sessions.insert(token.clone(), session_info);
        }

        Ok(token)
    }

    fn validate_token_internal(&self, token: &str) -> Result<Claims, AppError> {
        let token_data = decode::<Claims>(
            token,
            &DecodingKey::from_secret(self.config.jwt_secret.as_ref()),
            &Validation::new(Algorithm::HS256),
        )
        .map_err(|e| AppError::Authentication(format!("Invalid token: {}", e)))?;

        // 檢查會話是否仍然活躍（如果存在的話）
        if let Ok(sessions) = self.active_sessions.lock() {
            if let Some(session) = sessions.get(token) {
                if !session.is_active || session.expires_at <= Utc::now() {
                    return Err(AppError::Authentication("Session expired or inactive".to_string()));
                }
            }
            // 如果找不到 session，不拋出錯誤，因為伺服器重啟會清空記憶體
            // 只要 JWT token 本身是有效的就可以
        }

        Ok(token_data.claims)
    }
}

#[async_trait]
impl AuthService for AuthServiceImpl {
    async fn register(&self, request: RegisterRequest) -> Result<User, AppError> {
        // 檢查使用者名稱是否已存在
        if let Some(_) = self.user_repo.find_by_username(&request.username).await.map_err(AppError::from)? {
            return Err(AppError::Validation("Username already exists".to_string()));
        }

        // 檢查電子郵件是否已存在
        if let Some(_) = self.user_repo.find_by_email(&request.email).await.map_err(AppError::from)? {
            return Err(AppError::Validation("Email already exists".to_string()));
        }

        // 驗證密碼強度
        if request.password.len() < 8 {
            return Err(AppError::Validation("密碼一定要8位數以上".to_string()));
        }

        // 雜湊密碼
        let password_hash = self.hash_password(&request.password)?;

        // 建立使用者
        let create_user = CreateUser {
            username: request.username,
            email: request.email,
            password_hash,
            pharmacy_name: request.pharmacy_name,
            phone: Some(request.phone.clone()),
            line_user_id: request.line_user_id,
            role_id: Some(2), // 臨時設為 admin 角色進行測試
            contact_person: Some(request.contact_person),
            mobile: Some(request.mobile),
            institution_code: Some(request.institution_code),
            address: Some(request.address),
        };

        self.user_repo.create_user(create_user).await.map_err(AppError::from)
    }

    async fn login(&self, request: LoginRequest) -> Result<LoginResponse, AppError> {
        // 查找使用者
        let user = self.user_repo
            .find_by_username(&request.username)
            .await.map_err(AppError::from)?
            .ok_or_else(|| AppError::Authentication("Invalid username or password".to_string()))?;

        // 驗證密碼
        if !self.verify_password(&request.password, &user.password_hash)? {
            return Err(AppError::Authentication("Invalid username or password".to_string()));
        }

        // 檢查用戶狀態
        match user.status.as_deref() {
            Some("pending") => {
                return Err(AppError::Authentication("您的帳號正在審核中，請等待管理員審核後方可登入".to_string()));
            }
            Some("rejected") => {
                let reason = user.rejection_reason.as_deref().unwrap_or("未提供原因");
                return Err(AppError::Authentication(format!("您的帳號申請已被拒絕，原因：{}。如有疑問請聯繫管理員", reason)));
            }
            Some("approved") | None => {
                // 允許登入
            }
            Some(other_status) => {
                return Err(AppError::Authentication(format!("帳號狀態異常：{}，請聯繫管理員", other_status)));
            }
        }

        // 獲取用戶權限資訊
        let permissions = self.role_repo
            .find_user_permissions(user.id)
            .await
            .map_err(AppError::from)?;

        // 產生 JWT token
        let token = self.generate_token(&user)?;
        let expires_in = 24 * 60 * 60; // 24 hours in seconds

        Ok(LoginResponse {
            token,
            user,
            expires_in,
            permissions,
        })
    }

    async fn validate_token(&self, token: &str) -> Result<Claims, AppError> {
        self.validate_token_internal(token)
    }

    async fn refresh_token(&self, request: RefreshTokenRequest) -> Result<LoginResponse, AppError> {
        // 驗證舊 token
        let claims = self.validate_token_internal(&request.token)?;

        // 查找使用者
        let user_id: i64 = claims.sub.parse()
            .map_err(|_| AppError::Authentication("Invalid user ID in token".to_string()))?;

        let user = self.user_repo
            .find_by_id(user_id)
            .await.map_err(AppError::from)?
            .ok_or_else(|| AppError::Authentication("User not found".to_string()))?;

        // 獲取用戶權限資訊
        let permissions = self.role_repo
            .find_user_permissions(user.id)
            .await
            .map_err(AppError::from)?;

        // 產生新 token
        let token = self.generate_token(&user)?;
        let expires_in = 24 * 60 * 60; // 24 hours in seconds

        Ok(LoginResponse {
            token,
            user,
            expires_in,
            permissions,
        })
    }

    async fn change_password(&self, user_id: i64, old_password: &str, new_password: &str) -> Result<(), AppError> {
        // 查找使用者
        let user = self.user_repo
            .find_by_id(user_id)
            .await.map_err(AppError::from)?
            .ok_or_else(|| AppError::NotFound("User not found".to_string()))?;

        // 驗證舊密碼
        if !self.verify_password(old_password, &user.password_hash)? {
            return Err(AppError::Authentication("Invalid old password".to_string()));
        }

        // 驗證新密碼強度
        if new_password.len() < 8 {
            return Err(AppError::Validation("新密碼一定要八位數字以上".to_string()));
        }

        // 雜湊新密碼
        let new_password_hash = self.hash_password(new_password)?;

        // 更新使用者密碼
        self.user_repo.update_password(user_id, &new_password_hash).await.map_err(AppError::from)?;

        Ok(())
    }

    async fn logout(&self, token: &str) -> Result<(), AppError> {
        // 驗證 token 格式
        self.validate_token_internal(token)?;

        // 從活躍會話中移除
        if let Ok(mut sessions) = self.active_sessions.lock() {
            sessions.remove(token);
        }

        Ok(())
    }

    async fn get_user_sessions(&self, user_id: i64) -> Result<Vec<SessionInfo>, AppError> {
        let sessions = self.active_sessions.lock()
            .map_err(|_| AppError::Internal("Failed to access session storage".to_string()))?;

        let user_sessions: Vec<SessionInfo> = sessions
            .values()
            .filter(|session| session.user_id == user_id && session.is_active)
            .filter(|session| session.expires_at > Utc::now()) // 過濾過期會話
            .cloned()
            .collect();

        Ok(user_sessions)
    }

    async fn get_user_by_id(&self, user_id: i64) -> Result<Option<User>, AppError> {
        self.user_repo.find_by_id(user_id).await.map_err(AppError::from)
    }

    async fn update_user_profile(&self, user_id: i64, request: crate::api::auth::UpdateProfileRequest) -> Result<User, AppError> {
        // 先取得現有使用者資料
        let mut user = self.user_repo
            .find_by_id(user_id)
            .await.map_err(AppError::from)?
            .ok_or_else(|| AppError::NotFound("User not found".to_string()))?;

        // 更新欄位（只更新提供的欄位）
        if let Some(pharmacy_name) = request.pharmacy_name {
            user.pharmacy_name = pharmacy_name;
        }
        if let Some(phone) = request.phone {
            user.phone = Some(phone);
        }
        if let Some(mobile) = request.mobile {
            user.mobile = Some(mobile);
        }
        if let Some(contact_person) = request.contact_person {
            user.contact_person = Some(contact_person);
        }
        if let Some(institution_code) = request.institution_code {
            user.institution_code = Some(institution_code);
        }
        if let Some(address) = request.address {
            user.address = Some(address);
        }
        if let Some(line_user_id) = request.line_user_id {
            user.line_user_id = Some(line_user_id);
        }
        if let Some(notification_email) = request.notification_email {
            user.notification_email = notification_email;
        }
        if let Some(notification_line) = request.notification_line {
            user.notification_line = notification_line;
        }

        // 更新時間戳
        user.updated_at = chrono::Utc::now();

        // 儲存到資料庫（移除通知欄位的更新，避免前端已移除但型別衝突）
        self.user_repo.update_user(user_id, crate::repositories::user::UpdateUser {
            username: None,
            email: None,
            pharmacy_name: Some(user.pharmacy_name.clone()),
            phone: user.phone.clone(),
            mobile: user.mobile.clone(),
            contact_person: user.contact_person.clone(),
            institution_code: user.institution_code.clone(),
            address: user.address.clone(),
            line_user_id: user.line_user_id.clone(),
            notification_email: None,
            notification_line: None,
        }).await.map_err(AppError::from)?;

        Ok(user)
    }

    async fn revoke_session(&self, user_id: i64, session_id: &str) -> Result<(), AppError> {
        // 驗證會話屬於該使用者
        if let Ok(mut sessions) = self.active_sessions.lock() {
            if let Some(session) = sessions.get(session_id) {
                if session.user_id != user_id {
                    return Err(AppError::Authorization("Cannot revoke session of another user".to_string()));
                }
                sessions.remove(session_id);
                Ok(())
            } else {
                Err(AppError::NotFound("Session not found".to_string()))
            }
        } else {
            Err(AppError::Internal("Failed to access session storage".to_string()))
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::{
        database::Database,
        config::Config,
        models::User,
    };
    use tempfile::NamedTempFile;
    use chrono::Utc;

    async fn create_test_auth_service() -> AuthServiceImpl {
        // 使用記憶體中的 PostgreSQL 測試資料庫
        // 注意：這需要一個運行中的 PostgreSQL 實例
        let database_url = std::env::var("TEST_DATABASE_URL")
            .expect("TEST_DATABASE_URL must be set for cloud database testing");
        // 在實際測試中，你可能需要使用 testcontainers 或類似工具
        // let database = Database::new(&database_url).await.unwrap();
        
        let config = Config {
            database_url: database_url.to_string(),
            jwt_secret: "test_secret_key_for_jwt_testing_12345".to_string(),
            server_port: 8080,
            email: crate::config::EmailConfig {
                smtp_host: "smtp.test.com".to_string(),
                smtp_port: 587,
                smtp_username: "<EMAIL>".to_string(),
                smtp_password: "test_password".to_string(),
                from_email: "<EMAIL>".to_string(),
            },
            line: crate::config::LineConfig {
                channel_access_token: "test_token".to_string(),
                channel_secret: "test_secret".to_string(),
            },
            gcp: crate::config::GcpConfig {
                project_id: "test_project".to_string(),
                storage_bucket: "test_bucket".to_string(),
                credentials_path: None,
            },
        };

        AuthServiceImpl::new(database.pool().clone(), config)
    }

    fn create_test_register_request() -> RegisterRequest {
        RegisterRequest {
            username: "testuser".to_string(),
            email: "<EMAIL>".to_string(),
            password: "testpassword123".to_string(),
            pharmacy_name: "測試藥局".to_string(),
            phone: Some("0912345678".to_string()),
            line_user_id: Some("line_test_123".to_string()),
        }
    }

    #[tokio::test]
    async fn test_register_success() {
        let auth_service = create_test_auth_service().await;
        let register_request = create_test_register_request();

        let result = auth_service.register(register_request).await;
        assert!(result.is_ok());

        let user = result.unwrap();
        assert_eq!(user.username, "testuser");
        assert_eq!(user.email, "<EMAIL>");
        assert_eq!(user.pharmacy_name, "測試藥局");
        assert_ne!(user.password_hash, "testpassword123"); // 密碼應該被雜湊
    }

    #[tokio::test]
    async fn test_register_duplicate_username() {
        let auth_service = create_test_auth_service().await;
        let register_request = create_test_register_request();

        // 第一次註冊應該成功
        let result1 = auth_service.register(register_request.clone()).await;
        assert!(result1.is_ok());

        // 第二次註冊相同使用者名稱應該失敗
        let result2 = auth_service.register(register_request).await;
        assert!(result2.is_err());
        assert!(matches!(result2.unwrap_err(), AppError::Validation(_)));
    }

    #[tokio::test]
    async fn test_register_weak_password() {
        let auth_service = create_test_auth_service().await;
        let mut register_request = create_test_register_request();
        register_request.password = "123".to_string(); // 太短的密碼

        let result = auth_service.register(register_request).await;
        assert!(result.is_err());
        assert!(matches!(result.unwrap_err(), AppError::Validation(_)));
    }

    #[tokio::test]
    async fn test_login_success() {
        let auth_service = create_test_auth_service().await;
        let register_request = create_test_register_request();

        // 先註冊使用者
        auth_service.register(register_request.clone()).await.unwrap();

        // 測試登入
        let login_request = LoginRequest {
            username: register_request.username,
            password: register_request.password,
        };

        let result = auth_service.login(login_request).await;
        assert!(result.is_ok());

        let login_response = result.unwrap();
        assert!(!login_response.token.is_empty());
        assert_eq!(login_response.user.username, "testuser");
        assert_eq!(login_response.expires_in, 24 * 60 * 60); // 24 小時
    }

    #[tokio::test]
    async fn test_login_invalid_username() {
        let auth_service = create_test_auth_service().await;

        let login_request = LoginRequest {
            username: "nonexistent".to_string(),
            password: "testpassword123".to_string(),
        };

        let result = auth_service.login(login_request).await;
        assert!(result.is_err());
        assert!(matches!(result.unwrap_err(), AppError::Authentication(_)));
    }

    #[tokio::test]
    async fn test_login_invalid_password() {
        let auth_service = create_test_auth_service().await;
        let register_request = create_test_register_request();

        // 先註冊使用者
        auth_service.register(register_request.clone()).await.unwrap();

        // 測試錯誤密碼登入
        let login_request = LoginRequest {
            username: register_request.username,
            password: "wrongpassword".to_string(),
        };

        let result = auth_service.login(login_request).await;
        assert!(result.is_err());
        assert!(matches!(result.unwrap_err(), AppError::Authentication(_)));
    }

    #[tokio::test]
    async fn test_validate_token_success() {
        let auth_service = create_test_auth_service().await;
        let register_request = create_test_register_request();

        // 先註冊並登入使用者
        let user = auth_service.register(register_request.clone()).await.unwrap();
        let login_request = LoginRequest {
            username: register_request.username,
            password: register_request.password,
        };
        let login_response = auth_service.login(login_request).await.unwrap();

        // 驗證 token
        let result = auth_service.validate_token(&login_response.token).await;
        assert!(result.is_ok());

        let claims = result.unwrap();
        assert_eq!(claims.user_id, user.id);
        assert_eq!(claims.username, user.username);
    }

    #[tokio::test]
    async fn test_validate_token_invalid() {
        let auth_service = create_test_auth_service().await;

        let result = auth_service.validate_token("invalid_token").await;
        assert!(result.is_err());
        assert!(matches!(result.unwrap_err(), AppError::Authentication(_)));
    }

    #[tokio::test]
    async fn test_refresh_token_success() {
        let auth_service = create_test_auth_service().await;
        let register_request = create_test_register_request();

        // 先註冊並登入使用者
        auth_service.register(register_request.clone()).await.unwrap();
        let login_request = LoginRequest {
            username: register_request.username,
            password: register_request.password,
        };
        let login_response = auth_service.login(login_request).await.unwrap();

        // 等待一秒確保時間戳不同
        tokio::time::sleep(tokio::time::Duration::from_secs(1)).await;
        
        // 刷新 token
        let old_token = login_response.token.clone();
        let refresh_request = RefreshTokenRequest {
            token: login_response.token,
        };
        let result = auth_service.refresh_token(refresh_request).await;
        assert!(result.is_ok());

        let new_login_response = result.unwrap();
        assert!(!new_login_response.token.is_empty());
        assert_ne!(new_login_response.token, old_token); // 新 token 應該不同
    }

    #[tokio::test]
    async fn test_change_password_success() {
        let auth_service = create_test_auth_service().await;
        let register_request = create_test_register_request();

        // 先註冊使用者
        let user = auth_service.register(register_request.clone()).await.unwrap();

        // 更改密碼
        let result = auth_service.change_password(
            user.id,
            &register_request.password,
            "newpassword123"
        ).await;
        assert!(result.is_ok());

        // 測試用新密碼登入
        let login_request = LoginRequest {
            username: register_request.username,
            password: "newpassword123".to_string(),
        };
        let login_result = auth_service.login(login_request).await;
        assert!(login_result.is_ok());
    }

    #[tokio::test]
    async fn test_change_password_invalid_old_password() {
        let auth_service = create_test_auth_service().await;
        let register_request = create_test_register_request();

        // 先註冊使用者
        let user = auth_service.register(register_request).await.unwrap();

        // 用錯誤的舊密碼嘗試更改密碼
        let result = auth_service.change_password(
            user.id,
            "wrongoldpassword",
            "newpassword123"
        ).await;
        assert!(result.is_err());
        assert!(matches!(result.unwrap_err(), AppError::Authentication(_)));
    }

    #[tokio::test]
    async fn test_change_password_weak_new_password() {
        let auth_service = create_test_auth_service().await;
        let register_request = create_test_register_request();

        // 先註冊使用者
        let user = auth_service.register(register_request.clone()).await.unwrap();

        // 用太弱的新密碼嘗試更改密碼
        let result = auth_service.change_password(
            user.id,
            &register_request.password,
            "123" // 太短
        ).await;
        assert!(result.is_err());
        assert!(matches!(result.unwrap_err(), AppError::Validation(_)));
    }

    #[tokio::test]
    async fn test_password_hashing() {
        let auth_service = create_test_auth_service().await;
        let password = "testpassword123";

        let hash1 = auth_service.hash_password(password).unwrap();
        let hash2 = auth_service.hash_password(password).unwrap();

        // 相同密碼的雜湊值應該不同（因為有 salt）
        assert_ne!(hash1, hash2);

        // 但都應該能驗證原始密碼
        assert!(auth_service.verify_password(password, &hash1).unwrap());
        assert!(auth_service.verify_password(password, &hash2).unwrap());

        // 錯誤密碼應該驗證失敗
        assert!(!auth_service.verify_password("wrongpassword", &hash1).unwrap());
    }

    #[tokio::test]
    async fn test_token_generation_and_validation() {
        let auth_service = create_test_auth_service().await;
        
        let user = User {
            id: 1,
            username: "testuser".to_string(),
            email: "<EMAIL>".to_string(),
            password_hash: "hash".to_string(),
            pharmacy_name: "測試藥局".to_string(),
            phone: Some("0912345678".to_string()),
            line_user_id: Some("line123".to_string()),
            notification_email: true,
            notification_line: false,
            created_at: Utc::now(),
            updated_at: Utc::now(),
        };

        // 產生 token
        let token = auth_service.generate_token(&user).unwrap();
        assert!(!token.is_empty());

        // 驗證 token
        let claims = auth_service.validate_token_internal(&token).unwrap();
        assert_eq!(claims.user_id, user.id);
        assert_eq!(claims.username, user.username);
        assert_eq!(claims.sub, user.id.to_string());
    }

    #[tokio::test]
    async fn test_logout_success() {
        let auth_service = create_test_auth_service().await;
        let register_request = create_test_register_request();

        // 先註冊並登入使用者
        auth_service.register(register_request.clone()).await.unwrap();
        let login_request = LoginRequest {
            username: register_request.username,
            password: register_request.password,
        };
        let login_response = auth_service.login(login_request).await.unwrap();

        // 驗證 token 有效
        let result = auth_service.validate_token(&login_response.token).await;
        assert!(result.is_ok());

        // 登出
        let logout_result = auth_service.logout(&login_response.token).await;
        assert!(logout_result.is_ok());

        // 驗證 token 已失效
        let result = auth_service.validate_token(&login_response.token).await;
        assert!(result.is_err());
    }

    #[tokio::test]
    async fn test_get_user_sessions() {
        let auth_service = create_test_auth_service().await;
        let register_request = create_test_register_request();

        // 先註冊使用者
        let user = auth_service.register(register_request.clone()).await.unwrap();

        // 建立多個會話
        let login_request = LoginRequest {
            username: register_request.username,
            password: register_request.password,
        };
        
        let login1 = auth_service.login(login_request.clone()).await.unwrap();
        tokio::time::sleep(tokio::time::Duration::from_millis(100)).await;
        let login2 = auth_service.login(login_request).await.unwrap();

        // 取得使用者會話
        let sessions = auth_service.get_user_sessions(user.id).await.unwrap();
        assert_eq!(sessions.len(), 2);

        // 登出一個會話
        auth_service.logout(&login1.token).await.unwrap();

        // 檢查剩餘會話
        let sessions = auth_service.get_user_sessions(user.id).await.unwrap();
        assert_eq!(sessions.len(), 1);
        assert_eq!(sessions[0].token_id, login2.token);
    }

    #[tokio::test]
    async fn test_session_expiry() {
        let auth_service = create_test_auth_service().await;
        let register_request = create_test_register_request();

        // 先註冊並登入使用者
        auth_service.register(register_request.clone()).await.unwrap();
        let login_request = LoginRequest {
            username: register_request.username,
            password: register_request.password,
        };
        let login_response = auth_service.login(login_request).await.unwrap();

        // 驗證 token 有效
        let result = auth_service.validate_token(&login_response.token).await;
        assert!(result.is_ok());

        // 手動設定會話為過期（在實際應用中，這會由時間自然過期）
        if let Ok(mut sessions) = auth_service.active_sessions.lock() {
            if let Some(session) = sessions.get_mut(&login_response.token) {
                session.expires_at = Utc::now() - Duration::hours(1); // 設為一小時前過期
            }
        }

        // 驗證 token 已過期
        let result = auth_service.validate_token(&login_response.token).await;
        assert!(result.is_err());
        assert!(matches!(result.unwrap_err(), AppError::Authentication(_)));
    }
}