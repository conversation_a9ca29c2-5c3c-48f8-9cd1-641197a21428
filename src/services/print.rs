use async_trait::async_trait;
use serde::{Serialize, Deserialize};
use chrono::{DateTime, Utc};

use crate::error::{AppError, AppResult};
use crate::models::OrderDetails;
use crate::services::order::OrderService;

/// 列印格式類型
#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
#[serde(rename_all = "lowercase")]
pub enum PrintFormat {
    Html,
}

/// 列印選項
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PrintOptions {
    pub format: PrintFormat,
    pub include_prices: bool,
}

impl Default for PrintOptions {
    fn default() -> Self {
        Self {
            format: PrintFormat::Html,
            include_prices: true,
        }
    }
}

/// 列印結果
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PrintResult {
    pub content: String,
    pub filename: String,
    pub generated_at: DateTime<Utc>,
}

/// 訂單列印資料
#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct OrderPrintData {
    pub order: OrderDetails,
    pub print_time: DateTime<Utc>,
    pub total_items: usize,
}

/// 列印服務特徵
#[async_trait]
pub trait PrintService: Send + Sync {
    /// 列印訂單（僅管理者可用）
    async fn print_order(&self, order_id: i64, options: PrintOptions) -> AppResult<PrintResult>;
}

/// 列印服務實作
pub struct PrintServiceImpl {
    order_service: std::sync::Arc<dyn OrderService>,
}

impl PrintServiceImpl {
    pub fn new(order_service: std::sync::Arc<dyn OrderService>) -> Self {
        Self { order_service }
    }

    /// 生成訂單HTML內容
    fn generate_order_html(&self, data: &OrderPrintData, options: &PrintOptions) -> String {
        let mut html = String::new();
        
        html.push_str(&format!(r#"
<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>訂單列印 - {}</title>
    <style>
        body {{ font-family: 'Microsoft JhengHei', Arial, sans-serif; margin: 20px; line-height: 1.6; }}
        .header {{ text-align: center; border-bottom: 2px solid #333; padding-bottom: 15px; margin-bottom: 25px; }}
        .header h1 {{ color: #333; margin: 0; font-size: 28px; }}
        .order-info {{ margin-bottom: 25px; background: #f8f9fa; padding: 15px; border-radius: 8px; }}
        .order-info table {{ width: 100%; border-collapse: collapse; }}
        .order-info td {{ padding: 8px; border-bottom: 1px solid #ddd; }}
        .order-info td:first-child {{ font-weight: bold; width: 120px; color: #555; }}
        .items-section {{ margin-bottom: 25px; }}
        .items-section h3 {{ color: #333; border-bottom: 1px solid #ddd; padding-bottom: 8px; }}
        .items-table {{ width: 100%; border-collapse: collapse; margin-bottom: 20px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }}
        .items-table th {{ background-color: #4a5568; color: white; padding: 12px 8px; text-align: left; font-weight: 600; }}
        .items-table td {{ border: 1px solid #e2e8f0; padding: 10px 8px; }}
        .items-table tbody tr:nth-child(even) {{ background-color: #f7fafc; }}
        .items-table tbody tr:hover {{ background-color: #edf2f7; }}
        .total-section {{ text-align: right; margin-top: 20px; }}
        .total {{ font-size: 18px; font-weight: bold; color: #2d3748; background: #e2e8f0; padding: 10px; border-radius: 6px; display: inline-block; }}
        .footer {{ margin-top: 40px; text-align: center; font-size: 14px; color: #666; border-top: 1px solid #ddd; padding-top: 15px; }}
        .status {{ 
            display: inline-block; 
            padding: 4px 8px; 
            border-radius: 4px; 
            font-size: 12px; 
            font-weight: bold; 
            text-transform: uppercase; 
        }}
        .status.pending {{ background-color: #fef5e7; color: #d69e2e; }}
        .status.completed {{ background-color: #f0fff4; color: #38a169; }}
        .status.cancelled {{ background-color: #fed7d7; color: #e53e3e; }}
        .stock-status {{ padding: 2px 6px; border-radius: 3px; font-size: 11px; }}
        .stock-in {{ background-color: #c6f6d5; color: #276749; }}
        .stock-out {{ background-color: #fed7d7; color: #c53030; }}
        @media print {{ 
            body {{ margin: 0; }} 
            .no-print {{ display: none; }}
        }}
    </style>
</head>
<body>
    <div class="header">
        <h1>藥品採購訂單</h1>
    </div>
    
    <div class="order-info">
        <table>
            <tr><td>訂單編號：</td><td>{}</td></tr>
            <tr><td>訂單日期：</td><td>{}</td></tr>
            <tr><td>訂單狀態：</td><td><span class="status {}">{}</span></td></tr>
            {}
        </table>
    </div>
"#,
            data.order.order.order_number,
            data.order.order.order_number,
            data.order.order.created_at.format("%Y-%m-%d %H:%M:%S"),
            format!("{:?}", data.order.order.status).to_lowercase(),
            format!("{:?}", data.order.order.status),
            data.order.order.notes.as_ref().map(|n| format!("<tr><td>備註：</td><td>{}</td></tr>", n)).unwrap_or_default()
        ));

        // 訂單項目表格
        html.push_str(r#"
    <div class="items-section">
        <h3>訂單項目</h3>
        <table class="items-table">
            <thead>
                <tr>
                    <th style="width: 40%;">產品名稱</th>
                    <th style="width: 15%;">健保碼</th>
                    <th style="width: 10%;">數量</th>"#);

        if options.include_prices {
            html.push_str("<th style=\"width: 15%;\">單價</th><th style=\"width: 15%;\">小計</th>");
        }

        html.push_str("<th style=\"width: 5%;\">狀態</th>");
        html.push_str("</tr></thead><tbody>");

        for item in &data.order.items {
            html.push_str(&format!(
                "<tr><td><strong>{}</strong></td><td>{}</td><td style=\"text-align: center;\">{}</td>",
                item.product_name,
                item.product_nhi_code,
                item.item.quantity
            ));

            if options.include_prices {
                html.push_str(&format!(
                    "<td style=\"text-align: right;\">NT$ {:.2}</td><td style=\"text-align: right;\">NT$ {:.2}</td>",
                    item.item.unit_price,
                    item.item.subtotal
                ));
            }

            let stock_status = if item.item.is_stock_out { 
                "<span class=\"stock-status stock-out\">缺貨</span>" 
            } else { 
                "<span class=\"stock-status stock-in\">有庫存</span>" 
            };
            html.push_str(&format!("<td style=\"text-align: center;\">{}</td>", stock_status));

            html.push_str("</tr>");
        }

        html.push_str("</tbody></table>");

        if options.include_prices {
            html.push_str(&format!(
                r#"<div class="total-section"><div class="total">總計：NT$ {:.2}</div></div>"#,
                data.order.order.total_amount
            ));
        }

        html.push_str(&format!(
            r#"
    </div>
    
    <div class="footer">
        <p>列印時間：{}</p>
        <p>共 {} 項商品</p>
        <p>藥品採購系統 - 管理者專用</p>
        <p style="margin-top: 15px; font-size: 10px; color: #888;">本系統由活力藥師網高藥師撰寫</p>
    </div>
</body>
</html>"#,
            data.print_time.format("%Y年%m月%d日 %H:%M:%S"),
            data.total_items
        ));

        html
    }
}

#[async_trait]
impl PrintService for PrintServiceImpl {
    async fn print_order(&self, order_id: i64, options: PrintOptions) -> AppResult<PrintResult> {
        // 獲取訂單詳情
        let order = self.order_service.get_order_details_by_id(order_id).await?
            .ok_or_else(|| AppError::NotFound("訂單不存在".to_string()))?;
        
        let print_data = OrderPrintData {
            total_items: order.items.len(),
            print_time: Utc::now(),
            order,
        };

        let content = self.generate_order_html(&print_data, &options);

        Ok(PrintResult {
            content,
            filename: format!("order_{}.html", print_data.order.order.order_number),
            generated_at: Utc::now(),
        })
    }
}