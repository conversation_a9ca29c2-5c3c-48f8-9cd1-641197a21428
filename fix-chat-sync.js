// 聊天同步修復腳本
console.log('開始聊天同步修復...');

// 檢查當前用戶狀態
function checkUserStatus() {
    const authToken = localStorage.getItem('authToken');
    const userData = localStorage.getItem('user');

    console.log('認證狀態檢查:');
    console.log('- authToken:', authToken ? '存在' : '不存在');
    console.log('- userData:', userData ? '存在' : '不存在');

    if (userData) {
        try {
            const user = JSON.parse(userData);
            console.log('- 用戶資料:', user);
            return user;
        } catch (error) {
            console.error('- 用戶資料解析錯誤:', error);
            return null;
        }
    }
    return null;
}

// 檢查聊天記錄
function checkChatHistory() {
    const chatHistory = localStorage.getItem('chatHistory');
    console.log('聊天記錄檢查:');

    if (!chatHistory) {
        console.log('- 沒有找到聊天記錄');
        return [];
    }

    try {
        const parsed = JSON.parse(chatHistory);
        console.log(`- 找到 ${parsed.length} 條聊天記錄`);
        parsed.forEach((msg, index) => {
            console.log(`  ${index + 1}. [${msg.type}] ${msg.user_name}: ${msg.message.substring(0, 50)}...`);
        });
        return parsed;
    } catch (error) {
        console.error('- 聊天記錄解析錯誤:', error);
        return [];
    }
}

// 修復聊天訊息儲存
function fixChatMessageSave() {
    // 重新定義 saveChatMessage 函數，添加更多調試信息
    window.saveChatMessage = function (message, type) {
        console.log('saveChatMessage 被調用:', { message, type });

        // 檢查 currentUser
        const currentUser = checkUserStatus();
        if (!currentUser) {
            console.error('currentUser 不存在，無法儲存訊息');
            alert('用戶未登入，無法發送訊息');
            return;
        }

        const chatMessage = {
            id: Date.now(),
            user_id: currentUser.id || currentUser.username,
            user_name: currentUser.pharmacy_name || currentUser.username,
            message: message,
            type: type,
            created_at: new Date().toISOString(),
            status: 'sent'
        };

        console.log('準備儲存的訊息:', chatMessage);

        // 儲存到 localStorage
        let chatHistory = JSON.parse(localStorage.getItem('chatHistory')) || [];
        chatHistory.push(chatMessage);
        localStorage.setItem('chatHistory', JSON.stringify(chatHistory));

        console.log('訊息已儲存，當前聊天記錄數量:', chatHistory.length);

        // 觸發管理員端更新（如果在同一個瀏覽器）
        if (typeof updateChatStats === 'function') {
            updateChatStats();
        }
        if (typeof loadChatList === 'function') {
            loadChatList();
        }
    };

    console.log('saveChatMessage 函數已修復');
}

// 修復管理員端載入
function fixAdminChatLoad() {
    // 重新定義 loadChatList 函數，添加更多調試信息
    window.loadChatList = function (filter = 'all') {
        console.log('loadChatList 被調用，篩選:', filter);

        const chatHistory = JSON.parse(localStorage.getItem('chatHistory')) || [];
        console.log('載入的聊天記錄數量:', chatHistory.length);

        const chatListEl = document.getElementById('chat-list');
        if (!chatListEl) {
            console.error('找不到 chat-list 元素');
            return;
        }

        // 按用戶分組
        const userChats = {};
        chatHistory.forEach(msg => {
            if (!userChats[msg.user_id]) {
                userChats[msg.user_id] = {
                    user_id: msg.user_id,
                    user_name: msg.user_name,
                    messages: [],
                    last_message: null,
                    last_message_time: null,
                    unread_count: 0,
                    status: 'read'
                };
            }
            userChats[msg.user_id].messages.push(msg);

            // 更新最後訊息
            if (!userChats[msg.user_id].last_message_time ||
                new Date(msg.created_at) > new Date(userChats[msg.user_id].last_message_time)) {
                userChats[msg.user_id].last_message = msg;
                userChats[msg.user_id].last_message_time = msg.created_at;
            }

            // 計算未讀訊息
            if (msg.type === 'user' && !msg.replied) {
                userChats[msg.user_id].unread_count++;
                userChats[msg.user_id].status = 'unread';
            }
        });

        console.log('分組後的用戶聊天:', userChats);

        // 轉換為陣列並排序
        let chatList = Object.values(userChats).sort((a, b) =>
            new Date(b.last_message_time) - new Date(a.last_message_time)
        );

        // 應用篩選
        if (filter === 'unread') {
            chatList = chatList.filter(chat => chat.unread_count > 0);
        } else if (filter === 'replied') {
            chatList = chatList.filter(chat => chat.unread_count === 0);
        }

        console.log('篩選後的聊天列表:', chatList);

        // 渲染聊天列表
        if (chatList.length === 0) {
            chatListEl.innerHTML = `
                <div class="no-chats">
                    <div class="no-chats-icon">💬</div>
                    <h4>暫無留言</h4>
                    <p>目前沒有用戶留言</p>
                </div>
            `;
            console.log('沒有聊天記錄可顯示');
            return;
        }

        chatListEl.innerHTML = chatList.map(chat => `
            <div class="chat-item ${chat.status}" onclick="openChatConversation('${chat.user_id}')">
                <div class="chat-avatar">👤</div>
                <div class="chat-info">
                    <div class="chat-user-name">${chat.user_name}</div>
                    <div class="chat-last-message">${chat.last_message ? chat.last_message.message : '暫無訊息'}</div>
                </div>
                <div class="chat-meta">
                    <div class="chat-time">${chat.last_message_time ? new Date(chat.last_message_time).toLocaleString('zh-TW', {
            month: 'short',
            day: 'numeric',
            hour: '2-digit',
            minute: '2-digit'
        }) : ''}</div>
                    <div class="chat-status ${chat.status}">
                        ${chat.status === 'unread' ? `未讀 (${chat.unread_count})` : '已讀'}
                    </div>
                </div>
            </div>
        `).join('');

        console.log('聊天列表已渲染');
    };

    console.log('loadChatList 函數已修復');
}

// 手動發送測試訊息
function sendTestMessage() {
    const currentUser = checkUserStatus();
    if (!currentUser) {
        console.error('用戶未登入，無法發送測試訊息');
        return;
    }

    const testMessage = `測試訊息 - ${new Date().toLocaleString()}`;
    console.log('發送測試訊息:', testMessage);

    if (typeof saveChatMessage === 'function') {
        saveChatMessage(testMessage, 'user');
    } else {
        console.error('saveChatMessage 函數不存在');
    }
}

// 手動刷新管理員聊天列表
function refreshAdminChat() {
    console.log('刷新管理員聊天列表...');

    if (typeof updateChatStats === 'function') {
        updateChatStats();
    }

    if (typeof loadChatList === 'function') {
        loadChatList();
    } else {
        console.error('loadChatList 函數不存在');
    }
}

// 執行修復
function runFix() {
    console.log('=== 開始聊天同步修復 ===');

    checkUserStatus();
    checkChatHistory();
    fixChatMessageSave();
    fixAdminChatLoad();

    console.log('=== 修復完成 ===');
    console.log('可用函數:');
    console.log('- sendTestMessage() - 發送測試訊息');
    console.log('- refreshAdminChat() - 刷新管理員聊天列表');
    console.log('- checkUserStatus() - 檢查用戶狀態');
    console.log('- checkChatHistory() - 檢查聊天記錄');
}

// 導出函數
window.sendTestMessage = sendTestMessage;
window.refreshAdminChat = refreshAdminChat;
window.checkUserStatus = checkUserStatus;
window.checkChatHistory = checkChatHistory;

// 自動執行修復
runFix();