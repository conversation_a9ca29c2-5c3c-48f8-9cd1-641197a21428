<!DOCTYPE html>
<html lang="zh-TW">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>台南社區藥局藥品供應平台</title>
    <link rel="stylesheet" href="css/style.css?v=20250813-stats-32px">
</head>

<body>
    <div class="container">
        <header>
            <div class="header-content">
                <div class="logo-title">
                    <img src="images/logo.png" alt="公司標誌" class="header-logo">
                    <div class="title-section">
                        <h1>台南社區藥局藥品供應平台</h1>
                        <p class="subtitle">行政院衛生署補(捐)助科技發展計畫</p>
                    </div>
                </div>
                <div id="user-info" class="user-info" style="display: none;">
                    <span id="user-display-name"></span>
                    <button id="logout-btn" class="btn btn-secondary">登出</button>
                </div>
            </div>
        </header>

        <!-- 登入表單 -->
        <div id="login-section" class="section">
            <div class="card">
                <h2>使用者登入</h2>
                <form id="login-form">
                    <div class="form-group">
                        <label for="username">使用者名稱:</label>
                        <input type="text" id="username" name="username" required>
                    </div>
                    <div class="form-group">
                        <label for="password">密碼:</label>
                        <input type="password" id="password" name="password" required>
                    </div>
                    <button type="submit" class="btn btn-primary">登入</button>
                </form>

                <div class="register-link">
                    <p>還沒有帳號？ <a href="#" id="show-register">註冊新帳號</a></p>
                </div>
            </div>
        </div>

        <!-- 註冊表單 -->
        <div id="register-section" class="section" style="display: none;">
            <div class="card">
                <h2>加入會員</h2>
                <form id="register-form">
                    <div class="form-row">
                        <div class="form-group-inline">
                            <label for="reg-username"><span class="required">*</span>帳號:</label>
                            <input type="text" id="reg-username" name="username" required>
                        </div>
                        <div class="form-group-inline">
                            <label for="reg-password"><span class="required">*</span>密碼:</label>
                            <input type="password" id="reg-password" name="password" required>
                        </div>
                        <div class="form-group-inline">
                            <label for="reg-password-confirm"><span class="required">*</span>確認密碼:</label>
                            <input type="password" id="reg-password-confirm" name="password_confirm" required>
                        </div>
                    </div>
                    <div class="form-row">
                        <div class="form-group-inline">
                            <label for="pharmacy-name"><span class="required">*</span>機構單位:</label>
                            <input type="text" id="pharmacy-name" name="pharmacy_name" required>
                        </div>
                        <div class="form-group-inline">
                            <label for="contact-person"><span class="required">*</span>聯絡人:</label>
                            <input type="text" id="contact-person" name="contact_person" required>
                        </div>
                        <div class="form-group-inline">
                            <label for="phone"><span class="required">*</span>聯絡電話:</label>
                            <input type="tel" id="phone" name="phone" required>
                        </div>
                        <div class="form-group-inline">
                            <label for="mobile"><span class="required">*</span>手機號碼:</label>
                            <input type="tel" id="mobile" name="mobile" required>
                        </div>
                    </div>
                    <div class="form-row">
                        <div class="form-group-inline">
                            <label for="institution-code"><span class="required">*</span>機構代號:</label>
                            <input type="text" id="institution-code" name="institution_code" required>
                        </div>
                        <div class="form-group-inline">
                            <label for="reg-email"><span class="required">*</span>電子郵件:</label>
                            <input type="email" id="reg-email" name="email" required>
                        </div>
                        <div class="form-group-inline">
                            <label for="address"><span class="required">*</span>聯絡地址:</label>
                            <input type="text" id="address" name="address" required>
                        </div>
                    </div>
                    <button type="submit" class="btn btn-primary">確定加入會員</button>
                    <button type="button" id="show-login" class="btn btn-secondary">返回登入</button>
                </form>
            </div>
        </div>

        <!-- 主要內容區域 -->
        <div id="main-content" class="section" style="display: none;">
            <nav class="nav-tabs">
                <button class="nav-tab active" data-tab="products">產品管理</button>
                <button class="nav-tab" data-tab="cart">🛒 購物車</button>
                <button class="nav-tab" data-tab="orders">訂單管理</button>
                <button class="nav-tab" data-tab="profile">個人資料</button>
                <button class="nav-tab" data-tab="promotions-view" id="promotions-tab" style="display: none;">📢
                    訊息</button>
                <button class="nav-tab" data-tab="contact">💬 與我聯絡</button>
                <button class="nav-tab" data-tab="admin" style="display: none;">系統管理</button>
            </nav>

            <!-- 產品管理 -->
            <div id="products-tab" class="tab-content active">
                <div class="card">
                    <div class="product-header">
                        <h3>🏥 查詢中心產品目錄</h3>
                        <div class="search-controls">
                            <div class="search-bar">
                                <label>搜尋條件：</label>
                                <input type="text" id="product-search" placeholder="輸入產品名稱、健保代碼或製造商...">
                                <button id="search-btn" class="btn btn-primary">查詢</button>
                                <button id="clear-search-btn" class="btn btn-secondary">清除</button>
                            </div>
                        </div>
                    </div>

                    <div class="products-grid-container">
                        <div class="products-grid-header">
                            <div class="grid-header-item">健保資訊</div>
                            <div class="grid-header-item">品名 / 成分</div>
                            <div class="grid-header-item">規格</div>
                            <div class="grid-header-item">單價</div>
                            <div class="grid-header-item">數量</div>
                            <div class="grid-header-item">功能</div>
                            <div class="grid-header-item">狀態</div>
                        </div>
                        <div id="products-grid-body" class="products-grid-body">
                            <!-- 產品卡片將在這裡動態載入 -->
                        </div>
                    </div>

                    <div class="table-pagination">
                        <div class="pagination-info">
                            <span id="pagination-info">顯示第 1-10 筆，共 0 筆資料</span>
                        </div>
                        <div class="pagination-controls">
                            <button id="prev-page" class="btn btn-secondary">上一頁</button>
                            <div class="page-input-group">
                                <span>第</span>
                                <input type="number" id="page-input" min="1" value="1"
                                    style="width: 60px; text-align: center;">
                                <span>頁</span>
                                <button id="goto-page" class="btn btn-primary">前往</button>
                            </div>
                            <button id="next-page" class="btn btn-secondary">下一頁</button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 購物車 -->
            <div id="cart-tab" class="tab-content">
                <div class="card">
                    <h3>🛒 購物車</h3>
                    <div id="cart-items">
                        <!-- 購物車項目將在這裡動態載入 -->
                    </div>
                    <div class="cart-notes">
                        <div class="form-group">
                            <label for="order-notes">訂單留言版:</label>
                            <textarea id="order-notes" placeholder="請輸入訂單備註（選填）" rows="3"
                                style="width: 100%; resize: vertical;"></textarea>
                        </div>
                    </div>
                    <div class="cart-summary">
                        <div class="total">
                            <strong>總計: NT$ <span id="cart-total">0</span></strong>
                        </div>
                        <button id="checkout-btn" class="btn btn-primary">💳 結帳</button>
                        <button id="clear-cart-btn" class="btn btn-secondary">🗑️ 清空購物車</button>
                    </div>
                </div>
            </div>

            <!-- 訂單管理 -->
            <div id="orders-tab" class="tab-content">
                <div class="card">
                    <div class="orders-header">
                        <h3 id="orders-title">訂單歷史</h3>
                        <div id="admin-order-controls" class="admin-controls" style="display: none;">
                            <div class="order-filters">
                                <select id="status-filter">
                                    <option value="">所有狀態</option>
                                    <option value="pending" selected>待處理</option>
                                    <option value="processing">檢貨中</option>
                                    <option value="shipped">已出貨</option>
                                </select>
                                <button id="apply-filters" class="btn btn-secondary">篩選</button>
                                <button id="clear-filters" class="btn btn-outline">清除</button>
                            </div>
                            <div class="batch-operations" id="batch-operations" style="display: none;">
                                <div class="batch-controls">
                                    <select id="batch-status-select" class="enlarged-status-select">
                                        <option value="">選擇狀態</option>
                                        <option value="pending">待處理</option>
                                        <option value="processing">檢貨中</option>
                                        <option value="shipped">已出貨</option>
                                    </select>
                                    <button id="select-all-orders" class="btn btn-outline">全選</button>
                                    <button id="deselect-all-orders" class="btn btn-outline">取消全選</button>
                                    <button id="apply-batch-status" class="btn btn-primary">批次更新狀態</button>
                                    <span id="selected-count" class="selected-count">已選擇 0 個訂單</span>
                                </div>
                            </div>
                            <div class="order-stats" id="order-stats">
                                <!-- 統計信息將在這裡顯示 -->
                            </div>
                        </div>
                    </div>
                    <div id="orders-list">
                        <!-- 訂單列表將在這裡動態載入 -->
                    </div>
                </div>
            </div>

            <!-- 個人資料 -->
            <div id="profile-tab" class="tab-content">
                <div class="card">
                    <h3>個人資料</h3>
                    <form id="profile-form">
                        <div class="form-row">
                            <div class="form-group-inline">
                                <label for="profile-username">帳號:</label>
                                <input type="text" id="profile-username" name="username" disabled readonly>
                            </div>
                            <div class="form-group-inline">
                                <label for="profile-email-addr">電子郵件:</label>
                                <input type="email" id="profile-email-addr" name="email" disabled readonly>
                            </div>
                        </div>
                        <div class="form-row">
                            <div class="form-group-inline">
                                <label for="profile-pharmacy-name">機構單位:</label>
                                <input type="text" id="profile-pharmacy-name" name="pharmacy_name">
                            </div>
                            <div class="form-group-inline">
                                <label for="profile-contact-person">聯絡人:</label>
                                <input type="text" id="profile-contact-person" name="contact_person">
                            </div>
                        </div>
                        <div class="form-row">
                            <div class="form-group-inline">
                                <label for="profile-phone">聯絡電話:</label>
                                <input type="tel" id="profile-phone" name="phone">
                            </div>
                            <div class="form-group-inline">
                                <label for="profile-mobile">手機號碼:</label>
                                <input type="tel" id="profile-mobile" name="mobile">
                            </div>
                        </div>
                        <div class="form-row">
                            <div class="form-group-inline">
                                <label for="profile-institution-code">機構代號:</label>
                                <input type="text" id="profile-institution-code" name="institution_code">
                            </div>
                            <div class="form-group-inline">
                                <label for="profile-address">聯絡地址:</label>
                                <input type="text" id="profile-address" name="address">
                            </div>
                        </div>
                        <!-- 通知設定已移除 -->
                        <div class="form-row">
                            <button type="submit" class="btn btn-primary">更新資料</button>
                            <button type="button" id="change-password-btn" class="btn btn-secondary">修改密碼</button>
                        </div>
                    </form>
                </div>
            </div>

            <!-- 促銷訊息查看 -->
            <div id="promotions-view-tab" class="tab-content">
                <div class="card">
                    <div class="promotions-view-header">
                        <h3>📢 促銷訊息</h3>
                        <div class="promotions-view-controls">
                            <button id="mark-all-read" class="btn btn-secondary">全部標為已讀</button>
                            <div class="view-filter">
                                <label for="view-filter-select">顯示:</label>
                                <select id="view-filter-select">
                                    <option value="all">全部訊息</option>
                                    <option value="unread">未讀訊息</option>
                                    <option value="active">活躍訊息</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    <div id="promotions-view-content" class="promotions-view-content">
                        <!-- 促銷訊息列表將在這裡顯示 -->
                    </div>
                    <div id="promotions-view-pagination" class="table-pagination">
                        <div class="pagination-info">
                            <span id="promotions-view-pagination-info">顯示第 1-10 筆，共 0 筆資料</span>
                        </div>
                        <div class="pagination-controls">
                            <button id="promotions-view-prev-page" class="btn btn-secondary">上一頁</button>
                            <div class="page-input-group">
                                <span>第</span>
                                <input type="number" id="promotions-view-page-input" min="1" value="1"
                                    style="width: 60px; text-align: center;">
                                <span>頁</span>
                                <button id="promotions-view-goto-page" class="btn btn-primary">前往</button>
                            </div>
                            <button id="promotions-view-next-page" class="btn btn-secondary">下一頁</button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 與我聯絡 -->
            <div id="contact-tab" class="tab-content">
                <div class="card">
                    <div class="contact-header">
                        <h3>💬 與我聯絡</h3>
                        <p class="contact-subtitle">有任何問題或建議，歡迎與我們即時對話</p>
                    </div>

                    <div class="chat-container">
                        <!-- 聊天訊息區域 -->
                        <div id="chat-messages" class="chat-messages">
                            <div class="system-message">
                                <div class="message-content">
                                    <div class="avatar">🤖</div>
                                    <div class="message-bubble system">
                                        <p>您好！我是客服助理，有什麼可以幫助您的嗎？</p>
                                        <span class="message-time">剛剛</span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 輸入區域 -->
                        <div class="chat-input-container">
                            <form id="chat-form" class="chat-form">
                                <div class="input-group">
                                    <textarea id="chat-input" placeholder="輸入您的訊息..." rows="1" required></textarea>
                                    <button type="submit" class="send-btn">
                                        <span>發送</span>
                                        <span class="send-icon">📤</span>
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 系統管理 -->
            <div id="admin-tab" class="tab-content">
                <div class="card">
                    <h3>📢 訊息發送管理</h3>

                    <!-- 系統管理子標籤 -->
                    <div class="admin-nav">
                        <button class="admin-nav-tab active" data-admin-tab="send-message">發送訊息</button>
                        <button class="admin-nav-tab" data-admin-tab="message-history">訊息記錄</button>
                        <button class="admin-nav-tab" data-admin-tab="message-templates">訊息範本</button>
                        <button class="admin-nav-tab" data-admin-tab="user-approval">審核身份</button>
                        <button class="admin-nav-tab" data-admin-tab="chat-management">💬 留言板</button>
                    </div>

                    <!-- 發送訊息 -->
                    <div id="send-message-admin" class="admin-tab-content active">
                        <div class="message-compose-header">
                            <div class="header-content">
                                <div class="header-icon">
                                    <i class="icon">📝</i>
                                </div>
                                <div class="header-text">
                                    <h3>撰寫新訊息</h3>
                                    <p>向用戶發送重要通知和訊息</p>
                                </div>
                            </div>
                        </div>

                        <div class="message-form-wrapper">
                            <form id="send-message-form" class="message-form">
                                <!-- 基本資訊區塊 -->
                                <div class="form-section">
                                    <div class="section-header">
                                        <h4><i class="section-icon">📋</i> 基本資訊</h4>
                                    </div>

                                    <div class="form-row">
                                        <div class="form-group">
                                            <label for="message-type" class="form-label">
                                                <span class="label-text">訊息類型</span>
                                                <span class="required">*</span>
                                            </label>
                                            <div class="select-wrapper">
                                                <select id="message-type" class="form-control select-enhanced" required>
                                                    <option value="">請選擇訊息類型</option>
                                                    <option value="promotion" data-icon="🎉">促銷活動</option>
                                                    <option value="announcement" data-icon="📢">系統公告</option>
                                                    <option value="maintenance" data-icon="🔧">維護通知</option>
                                                    <option value="urgent" data-icon="⚠️">緊急通知</option>
                                                </select>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="form-group">
                                        <label for="message-title" class="form-label">
                                            <span class="label-text">訊息標題</span>
                                            <span class="required">*</span>
                                        </label>
                                        <input type="text" id="message-title" class="form-control input-enhanced"
                                            placeholder="輸入吸引人的標題..." required>
                                        <div class="input-hint">建議標題長度 10-50 字</div>
                                    </div>
                                </div>

                                <!-- 內容區塊 -->
                                <div class="form-section">
                                    <div class="section-header">
                                        <h4><i class="section-icon">✏️</i> 訊息內容</h4>
                                    </div>

                                    <div class="form-group">
                                        <label for="message-content" class="form-label">
                                            <span class="label-text">內容詳情</span>
                                            <span class="required">*</span>
                                        </label>
                                        <div class="textarea-wrapper">
                                            <textarea id="message-content" class="form-control textarea-enhanced"
                                                rows="8" placeholder="請輸入詳細的訊息內容..." required></textarea>
                                            <div class="char-counter">
                                                <span id="char-count">0</span> / 500 字
                                            </div>
                                        </div>
                                        <div class="input-hint">支援換行，建議內容長度 20-500 字</div>
                                    </div>
                                </div>

                                <!-- 發送設定區塊 -->
                                <div class="form-section">
                                    <div class="section-header">
                                        <h4><i class="section-icon">🚀</i> 發送設定</h4>
                                    </div>

                                    <div class="form-group">
                                        <label class="form-label">
                                            <span class="label-text">發送方式</span>
                                            <span class="required">*</span>
                                        </label>
                                        <div class="send-methods-grid">
                                            <label class="method-card">
                                                <input type="checkbox" id="send-web" checked>
                                                <div class="method-content">
                                                    <div class="method-icon">🌐</div>
                                                    <div class="method-info">
                                                        <div class="method-name">網站通知</div>
                                                        <div class="method-desc">在網站內顯示通知</div>
                                                    </div>
                                                </div>
                                            </label>

                                            <label class="method-card">
                                                <input type="checkbox" id="send-email">
                                                <div class="method-content">
                                                    <div class="method-icon">📧</div>
                                                    <div class="method-info">
                                                        <div class="method-name">電子郵件</div>
                                                        <div class="method-desc">發送到用戶信箱</div>
                                                    </div>
                                                </div>
                                            </label>

                                            <label class="method-card">
                                                <input type="checkbox" id="send-line">
                                                <div class="method-content">
                                                    <div class="method-icon">💬</div>
                                                    <div class="method-info">
                                                        <div class="method-name">Line 通知</div>
                                                        <div class="method-desc">透過 Line 推送</div>
                                                    </div>
                                                </div>
                                            </label>
                                        </div>
                                    </div>

                                    <div class="form-group">
                                        <label for="schedule-send" class="form-label">
                                            <span class="label-text">排程發送</span>
                                            <span class="optional">(選填)</span>
                                        </label>
                                        <div class="datetime-wrapper">
                                            <input type="datetime-local" id="schedule-send"
                                                class="form-control datetime-enhanced">
                                            <div class="datetime-hint">
                                                <i class="hint-icon">💡</i>
                                                留空表示立即發送，或選擇未來時間進行排程
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- 操作按鈕 -->
                                <div class="form-actions-enhanced">
                                    <button type="button" id="preview-message-btn" class="btn btn-outline">
                                        <i class="btn-icon">👁️</i>
                                        <span>預覽訊息</span>
                                    </button>
                                    <button type="submit" class="btn btn-primary-enhanced">
                                        <i class="btn-icon">🚀</i>
                                        <span>發送訊息</span>
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>

                    <!-- 訊息記錄 -->
                    <div id="message-history-admin" class="admin-tab-content">
                        <div class="admin-header">
                            <h4>訊息發送記錄</h4>
                            <div class="filter-controls">
                                <select id="history-filter-type" class="form-control">
                                    <option value="">所有類型</option>
                                    <option value="promotion">促銷活動</option>
                                    <option value="announcement">系統公告</option>
                                    <option value="maintenance">維護通知</option>
                                    <option value="urgent">緊急通知</option>
                                </select>
                                <input type="date" id="history-filter-date" class="form-control">
                                <button id="filter-history-btn" class="btn btn-secondary">篩選</button>
                            </div>
                        </div>

                        <div class="message-history-container">
                            <table id="message-history-table" class="admin-table">
                                <thead>
                                    <tr>
                                        <th>發送時間</th>
                                        <th>訊息類型</th>
                                        <th>標題</th>
                                        <th>發送對象</th>
                                        <th>發送方式</th>
                                        <th>狀態</th>
                                        <th>操作</th>
                                    </tr>
                                </thead>
                                <tbody id="message-history-body">
                                    <!-- 訊息記錄將在這裡動態載入 -->
                                </tbody>
                            </table>
                        </div>
                    </div>

                    <!-- 訊息範本 -->
                    <div id="message-templates-admin" class="admin-tab-content">
                        <div class="templates-header">
                            <div class="header-content">
                                <div class="header-icon">
                                    <i class="icon">📝</i>
                                </div>
                                <div class="header-text">
                                    <h3>訊息範本管理</h3>
                                    <p>使用預設範本快速建立訊息，或建立自己的範本</p>
                                </div>
                            </div>
                            <button id="create-template-btn" class="btn btn-primary-enhanced">
                                <i class="btn-icon">➕</i>
                                <span>新增範本</span>
                            </button>
                        </div>

                        <div class="templates-usage-guide">
                            <div class="guide-content">
                                <h4>💡 如何使用範本</h4>
                                <div class="guide-steps">
                                    <div class="step">
                                        <span class="step-number">1</span>
                                        <span class="step-text">點擊任意範本卡片或「使用範本」按鈕</span>
                                    </div>
                                    <div class="step">
                                        <span class="step-number">2</span>
                                        <span class="step-text">系統會自動切換到「發送訊息」頁面並填入範本內容</span>
                                    </div>
                                    <div class="step">
                                        <span class="step-number">3</span>
                                        <span class="step-text">您可以修改內容後直接發送，或儲存為新範本</span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="templates-container">
                            <div class="templates-grid" id="templates-grid">
                                <!-- 範本卡片將在這裡動態載入 -->
                            </div>
                        </div>
                    </div>

                    <!-- 審核身份 -->
                    <div id="user-approval-admin" class="admin-tab-content">
                        <div class="approval-header">
                            <div class="header-content">
                                <div class="header-icon">
                                    <i class="icon">👥</i>
                                </div>
                                <div class="header-text">
                                    <h3>用戶身份審核</h3>
                                    <p>審核新註冊用戶的身份資料</p>
                                </div>
                            </div>
                            <div class="approval-stats">
                                <div class="stat-card">
                                    <div class="stat-number" id="pending-count">0</div>
                                    <div class="stat-label">待審核</div>
                                </div>
                                <div class="stat-card">
                                    <div class="stat-number" id="approved-today">0</div>
                                    <div class="stat-label">今日已審核</div>
                                </div>
                            </div>
                        </div>

                        <div class="approval-controls">
                            <div class="batch-actions">
                                <button id="select-all-users" class="btn btn-outline">全選</button>
                                <button id="deselect-all-users" class="btn btn-outline">取消全選</button>
                                <button id="batch-approve" class="btn btn-success" disabled>批量通過</button>
                                <button id="batch-reject" class="btn btn-danger" disabled>批量拒絕</button>
                                <span id="selected-users-count" class="selected-count">已選擇 0 個用戶</span>
                            </div>
                            <div class="filter-controls">
                                <input type="text" id="user-search" placeholder="搜尋用戶名稱或機構..." class="form-control">
                                <button id="refresh-pending" class="btn btn-secondary">🔄 重新整理</button>
                            </div>
                        </div>

                        <div class="pending-users-container">
                            <div id="pending-users-list" class="pending-users-list">
                                <!-- 待審核用戶列表將在這裡動態載入 -->
                            </div>
                        </div>
                    </div>

                    <!-- 留言板管理 -->
                    <div id="chat-management-admin" class="admin-tab-content">
                        <div class="chat-management-header">
                            <div class="header-content">
                                <div class="header-text">
                                    <h3>留言板管理</h3>
                                    <p>查看和回覆用戶留言</p>
                                </div>
                            </div>
                            <div class="chat-stats">
                                <div class="stat-item">
                                    <span class="stat-number" id="unread-count">0</span>
                                    <span class="stat-label">未讀訊息</span>
                                </div>
                                <div class="stat-item">
                                    <span class="stat-number" id="total-chats">0</span>
                                    <span class="stat-label">總對話數</span>
                                </div>
                            </div>
                        </div>

                        <div class="chat-management-controls">
                            <div class="filter-tabs">
                                <button class="filter-tab active" data-filter="all">全部</button>
                                <button class="filter-tab" data-filter="unread">未讀</button>
                                <button class="filter-tab" data-filter="replied">已回覆</button>
                            </div>
                            <div class="search-controls">
                                <input type="text" id="chat-search" placeholder="搜尋用戶名稱或訊息內容..." class="form-control">
                                <button id="chat-search-btn" class="btn btn-secondary">🔍 搜尋</button>
                            </div>
                        </div>

                        <div class="chat-list-container">
                            <div id="chat-list" class="chat-list">
                                <!-- 聊天列表將在這裡動態載入 -->
                                <div class="no-chats">
                                    <div class="no-chats-icon">💬</div>
                                    <h4>暫無留言</h4>
                                    <p>目前沒有用戶留言</p>
                                </div>
                            </div>
                        </div>

                        <!-- 聊天對話視窗 -->
                        <div id="chat-conversation" class="chat-conversation" style="display: none;">
                            <div class="conversation-header">
                                <button id="back-to-list" class="btn btn-outline">← 返回列表</button>
                                <div class="user-info">
                                    <h4 id="conversation-user-name">用戶名稱</h4>
                                    <span id="conversation-user-info">聯絡資訊</span>
                                </div>
                                <div class="conversation-actions">
                                    <button id="mark-resolved" class="btn btn-success">標記已解決</button>
                                </div>
                            </div>

                            <div class="conversation-messages" id="conversation-messages">
                                <!-- 對話訊息將在這裡顯示 -->
                            </div>

                            <div class="conversation-reply">
                                <form id="admin-reply-form">
                                    <div class="reply-input-group">
                                        <textarea id="admin-reply-input" placeholder="輸入回覆訊息..." rows="3"
                                            required></textarea>
                                        <button type="submit" class="btn btn-primary">
                                            <span>發送回覆</span>
                                            <span class="send-icon">📤</span>
                                        </button>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 載入指示器 -->
        <div id="loading" class="loading" style="display: none;">
            <div class="spinner"></div>
            <p>載入中...</p>
        </div>

        <!-- 訊息提示 -->
        <div id="message" class="message" style="display: none;"></div>

        <!-- 訊息預覽模態框 -->
        <div id="message-preview-modal" class="modal" style="display: none;">
            <div class="modal-content">
                <div class="modal-header">
                    <h3>訊息預覽</h3>
                    <span class="close" id="close-message-preview-modal">&times;</span>
                </div>
                <div class="modal-body">
                    <div id="message-preview-content">
                        <!-- 訊息預覽內容將在這裡顯示 -->
                    </div>
                    <div class="modal-actions">
                        <button type="button" id="confirm-send-message" class="btn btn-primary">確認發送</button>
                        <button type="button" id="cancel-send-message" class="btn btn-secondary">取消</button>
                    </div>
                </div>
            </div>
        </div>

        <!-- 範本編輯模態框 -->
        <div id="template-modal" class="modal" style="display: none;">
            <div class="modal-content">
                <div class="modal-header">
                    <h3 id="template-modal-title">編輯範本</h3>
                    <span class="close" id="close-template-modal">&times;</span>
                </div>
                <div class="modal-body">
                    <form id="template-form">
                        <input type="hidden" id="template-id">
                        <div class="form-group">
                            <label for="template-name">範本名稱:</label>
                            <input type="text" id="template-name" name="name" required>
                        </div>
                        <div class="form-group">
                            <label for="template-type">範本類型:</label>
                            <select id="template-type" name="type" class="form-control" required>
                                <option value="promotion">促銷活動</option>
                                <option value="announcement">系統公告</option>
                                <option value="maintenance">維護通知</option>
                                <option value="urgent">緊急通知</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="template-title">預設標題:</label>
                            <input type="text" id="template-title" name="title" required>
                        </div>
                        <div class="form-group">
                            <label for="template-content">範本內容:</label>
                            <textarea id="template-content" name="content" rows="6" required></textarea>
                        </div>
                        <div class="modal-actions">
                            <button type="submit" class="btn btn-primary">儲存範本</button>
                            <button type="button" id="cancel-template-edit" class="btn btn-secondary">取消</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- 訊息詳情模態框 -->
        <div id="message-detail-modal" class="modal" style="display: none;">
            <div class="modal-content">
                <div class="modal-header">
                    <h3>訊息詳情</h3>
                    <span class="close" id="close-message-detail-modal">&times;</span>
                </div>
                <div class="modal-body">
                    <div id="message-detail-content">
                        <!-- 訊息詳情將在這裡顯示 -->
                    </div>
                </div>
            </div>
        </div>

        <!-- 用戶詳情模態框 -->
        <div id="user-detail-modal" class="modal" style="display: none;">
            <div class="modal-content">
                <div class="modal-header">
                    <h3>用戶詳細資料</h3>
                    <span class="close" id="close-user-detail-modal">&times;</span>
                </div>
                <div class="modal-body">
                    <div id="user-detail-content">
                        <!-- 用戶詳情將在這裡顯示 -->
                    </div>
                </div>
            </div>
        </div>

        <!-- 拒絕原因模態框 -->
        <div id="reject-reason-modal" class="modal" style="display: none;">
            <div class="modal-content">
                <div class="modal-header">
                    <h3>拒絕原因</h3>
                    <span class="close" id="close-reject-reason-modal">&times;</span>
                </div>
                <div class="modal-body">
                    <form id="reject-reason-form">
                        <input type="hidden" id="reject-user-id">
                        <div class="form-group">
                            <label for="reject-reason">請說明拒絕原因：</label>
                            <textarea id="reject-reason" name="reason" rows="4" placeholder="請詳細說明拒絕此用戶註冊的原因..."
                                required></textarea>
                        </div>
                        <div class="modal-actions">
                            <button type="submit" class="btn btn-danger">確認拒絕</button>
                            <button type="button" id="cancel-reject" class="btn btn-secondary">取消</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- 訂單詳情模態框 -->
        <div id="order-detail-modal" class="modal" style="display: none;">
            <div class="modal-content">
                <div class="modal-header">
                    <h3 id="order-detail-title">訂單詳情</h3>
                    <span class="close" id="close-order-detail-modal">&times;</span>
                </div>
                <div class="modal-body">
                    <div id="order-detail-content">
                        <!-- 訂單詳情將在這裡動態載入 -->
                    </div>
                </div>
            </div>
        </div>

        <!-- 產品編輯模態框 -->
        <div id="product-edit-modal" class="modal" style="display: none;">
            <div class="modal-content large-modal">
                <div class="modal-header">
                    <h3 id="product-edit-title">編輯產品</h3>
                    <span class="close" id="close-product-edit-modal">&times;</span>
                </div>
                <div class="modal-body">
                    <form id="product-edit-form">
                        <input type="hidden" id="edit-product-id">

                        <!-- 基本資訊 -->
                        <div class="form-section">
                            <h4>基本資訊</h4>
                            <div class="form-row">
                                <div class="form-group">
                                    <label for="edit-product-name">產品名稱 *</label>
                                    <input type="text" id="edit-product-name" name="name" required>
                                </div>
                                <div class="form-group">
                                    <label for="edit-nhi-code">健保代碼</label>
                                    <input type="text" id="edit-nhi-code" name="nhi_code">
                                </div>
                            </div>

                            <div class="form-row">
                                <div class="form-group">
                                    <label for="edit-dosage-form">規格</label>
                                    <input type="text" id="edit-dosage-form" name="dosage_form"
                                        placeholder="例: 30粒/盒, 5ml/瓶">
                                </div>
                                <div class="form-group">
                                    <label for="edit-manufacturer">製造商</label>
                                    <input type="text" id="edit-manufacturer" name="manufacturer">
                                </div>
                            </div>


                        </div>

                        <!-- 價格資訊 -->
                        <div class="form-section">
                            <h4>價格資訊</h4>
                            <div class="form-row">
                                <div class="form-group">
                                    <label for="edit-nhi-price">健保價 (元)</label>
                                    <input type="number" id="edit-nhi-price" name="nhi_price" step="0.01" min="0">
                                </div>
                                <div class="form-group">
                                    <label for="edit-unit-price">單價 (元)</label>
                                    <input type="number" id="edit-unit-price" name="unit_price" step="0.01" min="0">
                                </div>
                            </div>
                        </div>

                        <!-- 庫存資訊 -->
                        <div class="form-section">
                            <h4>庫存資訊</h4>
                            <div class="form-row">
                                <div class="form-group">
                                    <label for="edit-stock-quantity">庫存數量</label>
                                    <input type="number" id="edit-stock-quantity" name="stock_quantity" min="0">
                                </div>
                                <div class="form-group">
                                    <label for="edit-unit">單位</label>
                                    <input type="text" id="edit-unit" name="unit" placeholder="例: 盒、瓶、片">
                                </div>
                            </div>
                        </div>

                        <!-- 其他資訊 -->
                        <div class="form-section">
                            <h4>其他資訊</h4>
                            <div class="form-group">
                                <label for="edit-description">產品描述</label>
                                <textarea id="edit-description" name="description" rows="3"></textarea>
                            </div>
                            <div class="form-group">
                                <label for="edit-ingredients">成分/規格</label>
                                <textarea id="edit-ingredients" name="ingredients" rows="2"></textarea>
                            </div>
                        </div>

                        <div class="modal-actions">
                            <button type="submit" class="btn btn-primary">儲存變更</button>
                            <button type="button" id="cancel-product-edit" class="btn btn-secondary">取消</button>
                            <button type="button" id="delete-product" class="btn btn-danger">刪除產品</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- 版權聲明 -->
    <footer class="system-footer">
        <p>本系統由活力藥師網高藥師撰寫</p>
    </footer>

    <script src="js/config.js"></script>
    <script src="js/app.js?v=20250813-font-size-fix"></script>
</body>

</html>