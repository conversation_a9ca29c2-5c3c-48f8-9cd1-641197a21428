<!DOCTYPE html>
<html lang="zh-TW">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>產品載入測試</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
        }

        .test-result {
            margin: 10px 0;
            padding: 10px;
            border-radius: 5px;
        }

        .success {
            background: #d4edda;
            color: #155724;
        }

        .error {
            background: #f8d7da;
            color: #721c24;
        }

        .info {
            background: #d1ecf1;
            color: #0c5460;
        }
    </style>
</head>

<body>
    <h1>產品載入測試</h1>
    <div id="test-results"></div>
    <button onclick="runTests()">運行測試</button>

    <script>
        const results = document.getElementById('test-results');

        function addResult(message, type = 'info') {
            const div = document.createElement('div');
            div.className = `test-result ${type}`;
            div.textContent = message;
            results.appendChild(div);
        }

        async function runTests() {
            results.innerHTML = '';
            addResult('開始測試...', 'info');

            // 測試 1: 檢查 API 連接
            try {
                const response = await fetch('http://localhost:8080/api/products?page=1&limit=10');
                const data = await response.json();

                if (data.success && data.data) {
                    addResult(`✅ API 連接成功，獲得 ${data.data.length} 個產品`, 'success');

                    // 測試產品數據結構
                    if (data.data.length > 0) {
                        const product = data.data[0];
                        addResult(`✅ 產品數據結構正常: ${product.name}`, 'success');
                        addResult(`產品詳情: ID=${product.id}, 價格=${product.selling_price}, 庫存=${product.stock_quantity}`, 'info');
                    }
                } else {
                    addResult('❌ API 返回錯誤: ' + (data.error || '未知錯誤'), 'error');
                }
            } catch (error) {
                addResult('❌ API 連接失敗: ' + error.message, 'error');
            }

            // 測試 2: 檢查 localStorage 中的認證狀態
            const authToken = localStorage.getItem('authToken');
            const tokenExpiry = localStorage.getItem('tokenExpiry');

            if (authToken) {
                addResult('✅ 找到認證 token', 'success');
                if (tokenExpiry) {
                    const expiry = new Date(parseInt(tokenExpiry));
                    const now = new Date();
                    if (expiry > now) {
                        addResult(`✅ Token 有效，到期時間: ${expiry.toLocaleString()}`, 'success');
                    } else {
                        addResult('❌ Token 已過期', 'error');
                    }
                }
            } else {
                addResult('❌ 沒有找到認證 token', 'error');
            }

            // 測試 3: 檢查用戶數據
            const userData = localStorage.getItem('user');
            if (userData) {
                try {
                    const user = JSON.parse(userData);
                    addResult(`✅ 用戶數據: ${user.username || user.pharmacy_name}`, 'success');
                    if (user.permissions) {
                        addResult(`用戶權限: ${user.permissions.role_name}`, 'info');
                    }
                } catch (error) {
                    addResult('❌ 用戶數據解析失敗', 'error');
                }
            } else {
                addResult('❌ 沒有找到用戶數據', 'error');
            }

            addResult('測試完成', 'info');
        }
    </script>
</body>

</html>