<!DOCTYPE html>
<html lang="zh-TW">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>聊天數據調試</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
        }

        .debug-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }

        pre {
            background: #f5f5f5;
            padding: 10px;
            border-radius: 3px;
            overflow-x: auto;
        }

        button {
            padding: 10px 15px;
            margin: 5px;
            background: #007bff;
            color: white;
            border: none;
            border-radius: 3px;
            cursor: pointer;
        }

        button:hover {
            background: #0056b3;
        }

        .error {
            color: red;
        }

        .success {
            color: green;
        }
    </style>
</head>

<body>
    <h1>聊天數據調試工具</h1>

    <div class="debug-section">
        <h3>操作</h3>
        <button onclick="checkChatHistory()">檢查聊天記錄</button>
        <button onclick="checkCurrentUser()">檢查當前用戶</button>
        <button onclick="addTestMessage()">添加測試訊息</button>
        <button onclick="clearChatHistory()">清除聊天記錄</button>
        <button onclick="exportData()">導出數據</button>
    </div>

    <div class="debug-section">
        <h3>聊天記錄 (chatHistory)</h3>
        <div id="chat-history-output">點擊"檢查聊天記錄"來查看數據</div>
    </div>

    <div class="debug-section">
        <h3>當前用戶 (currentUser)</h3>
        <div id="current-user-output">點擊"檢查當前用戶"來查看數據</div>
    </div>

    <div class="debug-section">
        <h3>localStorage 所有鍵值</h3>
        <div id="localstorage-keys">
            <button onclick="showAllKeys()">顯示所有 localStorage 鍵值</button>
            <div id="keys-output"></div>
        </div>
    </div>

    <div class="debug-section">
        <h3>操作日誌</h3>
        <div id="log-output"></div>
    </div>

    <script>
        function log(message, type = 'info') {
            const logOutput = document.getElementById('log-output');
            const timestamp = new Date().toLocaleTimeString();
            const className = type === 'error' ? 'error' : type === 'success' ? 'success' : '';
            logOutput.innerHTML += `<div class="${className}">[${timestamp}] ${message}</div>`;
            logOutput.scrollTop = logOutput.scrollHeight;
        }

        function checkChatHistory() {
            try {
                const chatHistory = localStorage.getItem('chatHistory');
                const output = document.getElementById('chat-history-output');

                if (!chatHistory) {
                    output.innerHTML = '<div class="error">沒有找到 chatHistory 數據</div>';
                    log('沒有找到 chatHistory 數據', 'error');
                    return;
                }

                const parsed = JSON.parse(chatHistory);
                output.innerHTML = `
                    <div class="success">找到 ${parsed.length} 條聊天記錄</div>
                    <pre>${JSON.stringify(parsed, null, 2)}</pre>
                `;
                log(`找到 ${parsed.length} 條聊天記錄`, 'success');

            } catch (error) {
                const output = document.getElementById('chat-history-output');
                output.innerHTML = `<div class="error">解析 chatHistory 時發生錯誤: ${error.message}</div>`;
                log(`解析 chatHistory 時發生錯誤: ${error.message}`, 'error');
            }
        }

        function checkCurrentUser() {
            try {
                const userData = localStorage.getItem('user');
                const authToken = localStorage.getItem('authToken');
                const output = document.getElementById('current-user-output');

                if (!userData) {
                    output.innerHTML = '<div class="error">沒有找到用戶數據</div>';
                    log('沒有找到用戶數據', 'error');
                    return;
                }

                const parsed = JSON.parse(userData);
                output.innerHTML = `
                    <div class="success">找到用戶數據</div>
                    <div><strong>認證狀態:</strong> ${authToken ? '已登入' : '未登入'}</div>
                    <pre>${JSON.stringify(parsed, null, 2)}</pre>
                `;
                log('找到用戶數據', 'success');

            } catch (error) {
                const output = document.getElementById('current-user-output');
                output.innerHTML = `<div class="error">解析用戶數據時發生錯誤: ${error.message}</div>`;
                log(`解析用戶數據時發生錯誤: ${error.message}`, 'error');
            }
        }

        function addTestMessage() {
            try {
                // 模擬用戶數據
                const testUser = {
                    id: 'test_user_123',
                    username: 'test_pharmacy',
                    pharmacy_name: '測試藥局'
                };

                const testMessage = {
                    id: Date.now(),
                    user_id: testUser.id,
                    user_name: testUser.pharmacy_name,
                    message: '這是一條測試訊息，時間：' + new Date().toLocaleString(),
                    type: 'user',
                    created_at: new Date().toISOString(),
                    status: 'sent'
                };

                let chatHistory = JSON.parse(localStorage.getItem('chatHistory')) || [];
                chatHistory.push(testMessage);
                localStorage.setItem('chatHistory', JSON.stringify(chatHistory));

                log('測試訊息已添加', 'success');
                checkChatHistory(); // 自動刷新顯示

            } catch (error) {
                log(`添加測試訊息時發生錯誤: ${error.message}`, 'error');
            }
        }

        function clearChatHistory() {
            if (confirm('確定要清除所有聊天記錄嗎？')) {
                localStorage.removeItem('chatHistory');
                log('聊天記錄已清除', 'success');
                checkChatHistory(); // 自動刷新顯示
            }
        }

        function showAllKeys() {
            const output = document.getElementById('keys-output');
            const keys = Object.keys(localStorage);

            if (keys.length === 0) {
                output.innerHTML = '<div>localStorage 是空的</div>';
                return;
            }

            let html = '<h4>所有 localStorage 鍵值:</h4><ul>';
            keys.forEach(key => {
                const value = localStorage.getItem(key);
                const preview = value.length > 100 ? value.substring(0, 100) + '...' : value;
                html += `<li><strong>${key}:</strong> ${preview}</li>`;
            });
            html += '</ul>';

            output.innerHTML = html;
            log(`找到 ${keys.length} 個 localStorage 鍵值`, 'success');
        }

        function exportData() {
            const data = {
                chatHistory: localStorage.getItem('chatHistory'),
                user: localStorage.getItem('user'),
                authToken: localStorage.getItem('authToken'),
                timestamp: new Date().toISOString()
            };

            const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = 'chat-debug-data.json';
            a.click();
            URL.revokeObjectURL(url);

            log('數據已導出', 'success');
        }

        // 頁面載入時自動檢查
        window.addEventListener('load', function () {
            log('調試工具已載入');
            checkChatHistory();
            checkCurrentUser();
        });
    </script>
</body>

</html>