[package]
name = "pharmacy-system"
version = "0.1.0"
edition = "2021"

[dependencies]
# Web framework
axum = { version = "0.7", features = ["multipart"] }
tokio = { version = "1.0", features = ["full"] }
tower = "0.4"
tower-http = { version = "0.5", features = ["cors", "trace", "fs"] }

# Database
sqlx = { version = "0.7", features = ["runtime-tokio-rustls", "postgres", "mysql", "chrono", "migrate", "rust_decimal"] }

# Serialization
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"

# Authentication
jsonwebtoken = "9.0"
bcrypt = "0.15"

# File processing
calamine = "0.22"
csv = "1.3"

# HTTP client for external APIs
reqwest = { version = "0.11", features = ["json"] }

# Email
lettre = "0.11"

# Date/Time
chrono = { version = "0.4", features = ["serde"] }

# Decimal handling
rust_decimal = { version = "1.32", features = ["serde"] }

# Error handling
thiserror = "1.0"
anyhow = "1.0"

# Logging
tracing = "0.1"
tracing-subscriber = { version = "0.3", features = ["env-filter"] }

# Environment variables
dotenvy = "0.15"

# UUID generation
uuid = { version = "1.0", features = ["v4", "serde"] }

# GCP Cloud Storage
google-cloud-storage = "0.15"
google-cloud-auth = "0.13"

# Async traits
async-trait = "0.1"

# Regex for validation
regex = "1.10"

# Validation
validator = { version = "0.16", features = ["derive"] }
async-stream = "0.3"
futures = "0.3"

# Rate limiting
governor = "0.6"
nonzero_ext = "0.3"

# Performance monitoring
metrics = "0.21"
metrics-exporter-prometheus = "0.12"

# Security
secrecy = { version = "0.8", features = ["serde"] }

# Static regex compilation
lazy_static = "1.4"

# HTML processing
html-escape = "0.2"
url = "2.4"

# File system operations
tokio-fs = "0.1"

# Cron scheduling (for backup scheduling)
tokio-cron-scheduler = "0.9"

[dev-dependencies]
axum-test = "14.0"
mockall = "0.12"
tempfile = "3.8"
rust_decimal_macros = "1.32"
chrono-tz = "0.8"