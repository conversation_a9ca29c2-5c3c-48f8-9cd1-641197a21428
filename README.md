# 藥品中盤商採購系統

一個使用 Rust 和 Axum 框架開發的現代化藥品採購管理系統。

## 🌟 最新更新

### ✨ 用戶身份審核系統 (2025-08-12)

全新的用戶身份審核功能已完成開發！

**主要功能：**
- 👥 新用戶註冊審核流程
- 🔍 管理員審核界面
- ✅ 單個/批量用戶審核
- 📊 審核統計數據
- 📝 審核日誌記錄
- 🔍 用戶搜索和篩選

**測試方式：**
1. 訪問 [測試頁面](http://localhost:8080/test-approval.html)
2. 或直接進入系統：管理員登入 → 系統管理 → 審核身份

## 配置總結

- **開發環境：** http://localhost:8080
- **生產環境：** https://order.53617503.xyz

### 🔧 已完成的設定

**配置檔案 (web/js/config.js)：**
- 自動環境偵測
- 為你的域名設定正確的 API_BASE

**應用程式 (web/js/app.js)：**
- 使用配置檔案的設定
- 自動根據環境選擇正確的 API 端點

**部署腳本 (deploy.sh)：**
- 支援不同環境的建置
- 自動化部署流程

**部署指南 (DEPLOYMENT.md)：**
- 詳細的部署步驟
- 服務器配置範例
- 故障排除指南

## 功能特色

- 🔐 使用者認證與授權 (JWT)
- 👥 **用戶身份審核系統** ✨ **新功能**
- 📦 產品管理與庫存追蹤
- 🛒 購物車與訂單管理
- 📊 Excel/CSV 檔案匯入
- 📧 Email 通知系統
- 💬 Line Bot 整合
- ☁️ GCP 雲端備份
- 📝 完整的 API 文檔
- 🧪 全面的測試覆蓋

## 快速開始

### 1. 環境需求

- Rust 1.70+
- SQLite 3

### 2. 安裝與設定

```bash
# 複製專案
git clone <repository-url>
cd pharmacy-system

# 執行資料庫遷移（包含用戶審核相關表格）
cargo run --bin migrate

# 設定環境變數
cp .env.example .env
# 編輯 .env 檔案，至少設定 JWT_SECRET

# 安裝依賴並編譯
cargo build
```

### 3. 啟動系統

#### 生產模式
```bash
./start.sh
```

#### 開發模式 (推薦)
```bash
./dev.sh
```

或直接使用 cargo：
```bash
cargo run --bin pharmacy-system
```

### 4. 存取系統

#### Web 介面 (推薦)
- **主頁面**: http://localhost:8080/
- **審核系統測試**: http://localhost:8080/test-approval.html
- 功能完整的 Web 介面，包含：
  - 使用者註冊/登入
  - 產品瀏覽和搜尋
  - 購物車管理
  - 訂單管理
  - 個人資料設定
  - **用戶身份審核** ✨

#### API 端點
- API 資訊: http://localhost:8080/api
- 健康檢查: http://localhost:8080/health
- 詳細健康檢查: http://localhost:8080/health/detailed

### 5. 快速測試

```bash
# 快速檢查系統狀態和可用端點
./quick_test.sh

# 完整 API 功能測試
./test_api.sh
```

## 👥 用戶審核系統

### 功能概述

新用戶註冊後需要管理員審核才能正常使用系統。

### 使用流程

#### 新用戶註冊
1. 用戶填寫註冊表單
2. 系統將用戶狀態設為 "pending"
3. 顯示「請等待管理員審核後方可登入」
4. 用戶無法登入直到審核通過

#### 管理員審核
1. 管理員登入系統
2. 進入「系統管理」→「審核身份」
3. 查看待審核用戶列表
4. 可以：
   - 單個審核（通過/拒絕）
   - 批量審核
   - 查看用戶詳細資料
   - 添加拒絕原因

### 資料庫結構

```sql
-- 用戶表添加審核相關欄位
ALTER TABLE users ADD COLUMN status VARCHAR(20) DEFAULT 'pending';
ALTER TABLE users ADD COLUMN approved_by INTEGER;
ALTER TABLE users ADD COLUMN approved_at TIMESTAMP;
ALTER TABLE users ADD COLUMN rejected_by INTEGER;
ALTER TABLE users ADD COLUMN rejected_at TIMESTAMP;
ALTER TABLE users ADD COLUMN rejection_reason TEXT;

-- 審核日誌表
CREATE TABLE user_approval_logs (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    user_id INTEGER NOT NULL,
    action VARCHAR(20) NOT NULL,
    reason TEXT,
    performed_by INTEGER NOT NULL,
    performed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    old_status VARCHAR(20),
    new_status VARCHAR(20),
    FOREIGN KEY (user_id) REFERENCES users(id),
    FOREIGN KEY (performed_by) REFERENCES users(id)
);
```

## API 端點

### 認證
- `POST /api/auth/register` - 使用者註冊
- `POST /api/auth/login` - 使用者登入
- `POST /api/auth/refresh` - 刷新 token
- `GET /api/auth/me` - 取得當前使用者資訊
- `GET /api/auth/profile` - 取得使用者個人資料
- `POST /api/auth/profile` - 更新使用者個人資料

### 用戶審核 ✨ 新功能
- `GET /api/admin/users/pending` - 取得待審核用戶列表
- `POST /api/admin/users/approve` - 審核用戶（通過/拒絕）
- `POST /api/admin/users/batch-approve` - 批量審核用戶
- `GET /api/admin/users/:user_id/logs` - 取得用戶審核日誌

### 產品管理
- `GET /api/products` - 取得產品清單
- `GET /api/products/:id` - 取得產品詳情
- `GET /api/products/nhi/:nhi_code` - 根據健保代碼取得產品
- `POST /api/products/import/csv` - 匯入 CSV 檔案
- `POST /api/products/import/excel` - 匯入 Excel 檔案

### 購物車
- `GET /api/cart` - 取得購物車內容
- `POST /api/cart` - 添加商品到購物車
- `DELETE /api/cart/clear` - 清空購物車
- `GET /api/cart/summary` - 取得購物車摘要

### 訂單管理
- `GET /api/orders` - 取得訂單清單
- `POST /api/orders` - 建立訂單
- `POST /api/orders/cart` - 從購物車建立訂單
- `GET /api/orders/:id` - 取得訂單詳情
- `GET /api/orders/number/:order_number` - 根據訂單編號取得訂單

## API 使用範例

### 1. 註冊新使用者（需要審核）
```bash
curl -X POST http://localhost:8080/api/auth/register \
  -H "Content-Type: application/json" \
  -d '{
    "username": "pharmacy_user",
    "email": "<EMAIL>",
    "password": "secure_password",
    "pharmacy_name": "我的藥局",
    "phone": "0912345678"
  }'
```

### 2. 管理員取得待審核用戶
```bash
curl -X GET http://localhost:8080/api/admin/users/pending \
  -H "Authorization: Bearer $ADMIN_TOKEN"
```

### 3. 審核用戶
```bash
curl -X POST http://localhost:8080/api/admin/users/approve \
  -H "Authorization: Bearer $ADMIN_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "user_id": 123,
    "action": "approve"
  }'
```

### 4. 批量審核用戶
```bash
curl -X POST http://localhost:8080/api/admin/users/batch-approve \
  -H "Authorization: Bearer $ADMIN_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "user_ids": [123, 124, 125],
    "action": "approve"
  }'
```

## 測試

### 執行所有測試
```bash
cargo test
```

### 執行整合測試
```bash
cargo test --test comprehensive_integration_test
```

### 執行端到端測試
```bash
cargo test --test end_to_end_test
```

## 環境變數說明

| 變數名稱 | 說明 | 預設值 |
|---------|------|--------|
| `DATABASE_URL` | 資料庫連線字串 | `******************************` |
| `JWT_SECRET` | JWT 簽名密鑰 | **必須設定** |
| `PORT` | 伺服器埠號 | `8080` |
| `SMTP_HOST` | SMTP 伺服器 | `smtp.gmail.com` |
| `SMTP_USERNAME` | SMTP 使用者名稱 | - |
| `SMTP_PASSWORD` | SMTP 密碼 | - |

## 開發工具

### 安裝 cargo-watch (自動重載)
```bash
cargo install cargo-watch
```

### 程式碼格式化
```bash
cargo fmt
```

### 程式碼檢查
```bash
cargo clippy
```

## 專案結構

```
src/
├── api/           # API 路由和處理器
│   ├── auth.rs    # 認證相關 API
│   ├── user_approval.rs  # 用戶審核 API ✨
│   └── ...
├── database/      # 資料庫連線和遷移
├── models/        # 資料模型
│   ├── user.rs    # 用戶模型（包含審核相關）
│   └── ...
├── repositories/  # 資料存取層
│   ├── user.rs    # 用戶資料存取（包含審核方法）
│   └── ...
├── services/      # 業務邏輯層
├── config.rs      # 配置管理
├── error.rs       # 錯誤處理
├── logging.rs     # 日誌系統
└── main.rs        # 應用程式入口

web/
├── index.html     # 主頁面（包含審核界面）
├── js/
│   └── app.js     # 前端邏輯（包含審核功能）
└── css/
    └── style.css  # 樣式（包含審核界面樣式）

migrations/
├── 20241201000000_create_users.sql
├── 20241201000001_add_user_approval_fields.sql  # 審核欄位 ✨
└── 20241201000002_create_user_approval_logs.sql # 審核日誌 ✨

tests/
├── comprehensive_integration_test.rs  # 整合測試
├── end_to_end_test.rs                # 端到端測試
└── ...
```

## 🎯 開發里程碑

- ✅ 基礎系統架構
- ✅ 用戶認證系統
- ✅ 產品管理功能
- ✅ 購物車與訂單
- ✅ **用戶身份審核系統** (2025-08-12)
- 🔄 進階權限管理
- 📋 報表系統
- 🔔 通知系統增強

## 貢獻指南

1. Fork 專案
2. 建立功能分支 (`git checkout -b feature/amazing-feature`)
3. 提交變更 (`git commit -m 'Add some amazing feature'`)
4. 推送到分支 (`git push origin feature/amazing-feature`)
5. 開啟 Pull Request

## 授權

本專案採用 MIT 授權 - 詳見 [LICENSE](LICENSE) 檔案。

---

## 📞 支援

如有問題或建議，請：
1. 查看 [測試頁面](http://localhost:8080/test-approval.html)
2. 檢查 [部署指南](DEPLOYMENT.md)
3. 提交 Issue 或 Pull Request

**最後更新：** 2025年8月12日 - 用戶審核系統完成 ✨