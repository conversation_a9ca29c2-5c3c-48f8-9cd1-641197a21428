// 訊息功能修復腳本
console.log('開始訊息功能修復...');

// 檢查訊息相關元素
function checkMessageElements() {
    const container = document.getElementById('promotions-view-content');
    const tab = document.getElementById('promotions-view-tab');
    const navTab = document.querySelector('[data-tab="promotions-view"]');

    console.log('訊息元素檢查:');
    console.log('- promotions-view-content:', !!container);
    console.log('- promotions-view-tab:', !!tab);
    console.log('- nav tab:', !!navTab);

    return { container, tab, navTab };
}

// 創建測試訊息
function createTestMessages() {
    const testMessages = [
        {
            id: 1,
            sent_at: new Date(Date.now() - 86400000).toISOString(),
            type: 'announcement',
            title: '系統維護通知',
            content: '系統將於本週末進行維護，維護期間可能無法正常使用，敬請見諒。',
            target_audience: 'all',
            send_methods: ['web'],
            status: 'sent'
        },
        {
            id: 2,
            sent_at: new Date().toISOString(),
            type: 'promotion',
            title: '新品上市優惠',
            content: '新品上市，全館商品 8 折優惠，歡迎選購！',
            target_audience: 'all',
            send_methods: ['web'],
            status: 'sent'
        },
        {
            id: 3,
            sent_at: new Date(Date.now() - 3600000).toISOString(),
            type: 'urgent',
            title: '緊急通知',
            content: '由於系統異常，部分功能暫時無法使用，我們正在緊急修復中。',
            target_audience: 'all',
            send_methods: ['web', 'email'],
            status: 'sent'
        }
    ];

    localStorage.setItem("messageHistory", JSON.stringify(testMessages));
    console.log('已創建測試訊息:', testMessages.length, '條');
    return testMessages;
}

// 簡化的訊息渲染函數
function simpleRenderMessages(messages) {
    const container = document.getElementById('promotions-view-content');
    if (!container) {
        console.error('找不到訊息容器');
        return;
    }

    if (messages.length === 0) {
        container.innerHTML = `
            <div style="text-align: center; padding: 40px; color: #999;">
                <div style="font-size: 48px;">📭</div>
                <h3>暫無訊息</h3>
                <p>目前沒有新的促銷訊息或通知</p>
            </div>
        `;
        return;
    }

    const typeLabels = {
        'promotion': '促銷活動',
        'announcement': '系統公告',
        'maintenance': '維護通知',
        'urgent': '緊急通知'
    };

    const typeIcons = {
        'promotion': '🎉',
        'announcement': '📢',
        'maintenance': '🔧',
        'urgent': '⚠️'
    };

    const typeColors = {
        'promotion': '#e8f5e8',
        'announcement': '#e3f2fd',
        'maintenance': '#fff3e0',
        'urgent': '#ffebee'
    };

    container.innerHTML = messages.map(message => `
        <div style="border: 1px solid #ddd; margin: 15px 0; padding: 20px; border-radius: 8px; background: white; box-shadow: 0 2px 4px rgba(0,0,0,0.1);">
            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 15px;">
                <div style="display: flex; align-items: center; gap: 10px;">
                    <span style="font-size: 24px;">${typeIcons[message.type]}</span>
                    <span style="background-color: ${typeColors[message.type]}; padding: 4px 12px; border-radius: 12px; font-size: 12px; font-weight: bold;">
                        ${typeLabels[message.type]}
                    </span>
                </div>
                <div style="color: #666; font-size: 14px;">
                    ${new Date(message.sent_at).toLocaleDateString('zh-TW')}
                </div>
            </div>
            
            <div>
                <h3 style="margin: 0 0 10px 0; color: #333; font-size: 18px;">${message.title}</h3>
                <div style="color: #666; line-height: 1.6; font-size: 14px;">
                    ${message.content.replace(/\n/g, '<br>')}
                </div>
            </div>
            
            <div style="margin-top: 15px; padding-top: 15px; border-top: 1px solid #eee; display: flex; justify-content: space-between; align-items: center;">
                <div style="font-size: 12px; color: #888;">
                    發送方式: ${Array.isArray(message.send_methods) ?
            message.send_methods.map(method => {
                const methodLabels = { web: '網站通知', email: '電子郵件', line: 'Line通知' };
                return methodLabels[method] || method;
            }).join(', ') :
            message.send_methods
        }
                </div>
                <button style="padding: 6px 12px; background: #f8f9fa; border: 1px solid #dee2e6; border-radius: 4px; cursor: pointer; font-size: 12px;" 
                        onclick="markAsRead(${message.id})">
                    標記為已讀
                </button>
            </div>
        </div>
    `).join('');

    console.log('已渲染', messages.length, '條訊息');
}

// 簡化的載入訊息函數
function simpleLoadMessages() {
    console.log('開始載入訊息...');

    const { container } = checkMessageElements();
    if (!container) {
        console.error('找不到訊息容器，無法載入訊息');
        return;
    }

    try {
        // 顯示載入中
        container.innerHTML = '<div style="text-align: center; padding: 40px;">載入中...</div>';

        // 從 localStorage 獲取訊息記錄
        let messageHistory = JSON.parse(localStorage.getItem("messageHistory")) || [];
        console.log('從 localStorage 讀取的訊息記錄:', messageHistory.length, '條');

        // 如果沒有訊息記錄，創建測試訊息
        if (messageHistory.length === 0) {
            console.log('沒有找到訊息記錄，創建測試訊息');
            messageHistory = createTestMessages();
        }

        // 過濾出已發送的訊息，按時間排序（最新的在前）
        const sentMessages = messageHistory
            .filter(msg => msg.status === 'sent')
            .sort((a, b) => new Date(b.sent_at) - new Date(a.sent_at));

        console.log('過濾後的已發送訊息數量:', sentMessages.length);

        simpleRenderMessages(sentMessages);

    } catch (error) {
        console.error('載入訊息錯誤:', error);
        container.innerHTML = `<div style="text-align: center; padding: 40px; color: red;">載入失敗: ${error.message}</div>`;
    }
}

// 標記為已讀函數
function markAsRead(messageId) {
    console.log('標記訊息為已讀:', messageId);

    // 獲取已讀訊息列表
    let readMessages = JSON.parse(localStorage.getItem('readMessages')) || [];

    if (!readMessages.includes(messageId)) {
        readMessages.push(messageId);
        localStorage.setItem('readMessages', JSON.stringify(readMessages));

        // 更新 UI
        const button = document.querySelector(`[onclick="markAsRead(${messageId})"]`);
        if (button) {
            button.textContent = '已讀';
            button.disabled = true;
            button.style.background = '#e9ecef';
            button.style.cursor = 'not-allowed';
        }

        console.log('已標記為已讀');
    }
}

// 等待 DOM 載入完成後執行
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', function () {
        setTimeout(() => {
            checkMessageElements();
        }, 1000);
    });
} else {
    setTimeout(() => {
        checkMessageElements();
    }, 1000);
}

// 導出函數供手動調用
window.simpleLoadMessages = simpleLoadMessages;
window.checkMessageElements = checkMessageElements;
window.markAsRead = markAsRead;

console.log('訊息修復腳本載入完成，可以手動調用 simpleLoadMessages() 來載入訊息');