// 快速修復訊息功能
console.log('開始快速修復訊息功能...');

// 重新定義 renderPromotionMessages 函數
window.renderPromotionMessages = function (messages) {
    console.log('執行修復版的 renderPromotionMessages，訊息數量:', messages.length);

    const container = document.getElementById('promotions-view-content');
    if (!container) {
        console.error('找不到促銷訊息容器');
        return;
    }

    if (messages.length === 0) {
        container.innerHTML = `
            <div class="no-messages">
                <div class="no-messages-icon">📭</div>
                <h3>暫無訊息</h3>
                <p>目前沒有新的促銷訊息或通知</p>
            </div>
        `;
        return;
    }

    const typeLabels = {
        'promotion': '促銷活動',
        'announcement': '系統公告',
        'maintenance': '維護通知',
        'urgent': '緊急通知'
    };

    const typeIcons = {
        'promotion': '🎉',
        'announcement': '📢',
        'maintenance': '🔧',
        'urgent': '⚠️'
    };

    const typeColors = {
        'promotion': '#e8f5e8',
        'announcement': '#e3f2fd',
        'maintenance': '#fff3e0',
        'urgent': '#ffebee'
    };

    try {
        const messagesHtml = messages.map(message => {
            // 處理發送方式
            let sendMethodsHtml = '';
            if (Array.isArray(message.send_methods)) {
                sendMethodsHtml = message.send_methods.map(method => {
                    const methodLabels = { web: '網站通知', email: '電子郵件', line: 'Line通知' };
                    return `<span class="method-tag">${methodLabels[method] || method}</span>`;
                }).join('');
            } else {
                sendMethodsHtml = `<span class="method-tag">${message.send_methods}</span>`;
            }

            return `
                <div class="promotion-message-card" data-type="${message.type}">
                    <div class="message-header">
                        <div class="message-type-info">
                            <span class="message-icon">${typeIcons[message.type] || '📄'}</span>
                            <span class="message-type-label" style="background-color: ${typeColors[message.type] || '#f0f0f0'}">
                                ${typeLabels[message.type] || message.type}
                            </span>
                        </div>
                        <div class="message-date">
                            ${new Date(message.sent_at).toLocaleDateString('zh-TW')}
                        </div>
                    </div>
                    
                    <div class="message-content">
                        <h3 class="message-title">${message.title || '無標題'}</h3>
                        <div class="message-text">
                            ${(message.content || '').replace(/\n/g, '<br>')}
                        </div>
                    </div>
                    
                    <div class="message-footer">
                        <div class="message-methods">
                            <span class="method-label">發送方式：</span>
                            ${sendMethodsHtml}
                        </div>
                        <button class="btn btn-outline btn-sm" onclick="markAsRead(${message.id})">
                            標記為已讀
                        </button>
                    </div>
                </div>
            `;
        }).join('');

        container.innerHTML = messagesHtml;
        console.log('訊息渲染完成');

    } catch (error) {
        console.error('渲染訊息時發生錯誤:', error);
        container.innerHTML = `
            <div class="no-messages">
                <div class="no-messages-icon">❌</div>
                <h3>載入錯誤</h3>
                <p>渲染訊息時發生錯誤: ${error.message}</p>
            </div>
        `;
    }
};

// 重新定義 loadPromotionMessages 函數
window.loadPromotionMessages = async function () {
    console.log('執行修復版的 loadPromotionMessages');

    try {
        // 顯示載入中（如果有 showLoading 函數）
        if (typeof showLoading === 'function') {
            showLoading();
        }

        // 從 localStorage 獲取管理員發送的訊息記錄
        let messageHistory = JSON.parse(localStorage.getItem("messageHistory")) || [];
        console.log('從 localStorage 讀取的訊息記錄:', messageHistory.length);

        // 如果沒有訊息記錄，創建一些測試訊息
        if (messageHistory.length === 0) {
            console.log('沒有找到訊息記錄，創建測試訊息');
            const testMessages = [
                {
                    id: 1,
                    sent_at: new Date(Date.now() - 86400000).toISOString(),
                    type: 'announcement',
                    title: '系統維護通知',
                    content: '系統將於本週末進行維護，維護期間可能無法正常使用，敬請見諒。',
                    target_audience: 'all',
                    send_methods: ['web'],
                    status: 'sent'
                },
                {
                    id: 2,
                    sent_at: new Date().toISOString(),
                    type: 'promotion',
                    title: '新品上市優惠',
                    content: '新品上市，全館商品 8 折優惠，歡迎選購！',
                    target_audience: 'all',
                    send_methods: ['web'],
                    status: 'sent'
                },
                {
                    id: 3,
                    sent_at: new Date(Date.now() - 3600000).toISOString(),
                    type: 'urgent',
                    title: '緊急通知',
                    content: '由於系統異常，部分功能暫時無法使用，我們正在緊急修復中。',
                    target_audience: 'all',
                    send_methods: ['web', 'email'],
                    status: 'sent'
                }
            ];
            localStorage.setItem("messageHistory", JSON.stringify(testMessages));
            messageHistory = testMessages;
        }

        // 過濾出已發送的訊息，按時間排序（最新的在前）
        const sentMessages = messageHistory
            .filter(msg => msg.status === 'sent')
            .sort((a, b) => new Date(b.sent_at) - new Date(a.sent_at));

        console.log('過濾後的已發送訊息數量:', sentMessages.length);

        window.renderPromotionMessages(sentMessages);

    } catch (error) {
        console.error('載入促銷訊息錯誤:', error);
        if (typeof showMessage === 'function') {
            showMessage('載入訊息失敗: ' + error.message, 'error');
        }

        // 顯示錯誤信息
        const container = document.getElementById('promotions-view-content');
        if (container) {
            container.innerHTML = `
                <div class="no-messages">
                    <div class="no-messages-icon">❌</div>
                    <h3>載入失敗</h3>
                    <p>載入訊息時發生錯誤: ${error.message}</p>
                    <button onclick="window.loadPromotionMessages()" style="margin-top: 10px; padding: 8px 16px; background: #007bff; color: white; border: none; border-radius: 4px; cursor: pointer;">
                        重試
                    </button>
                </div>
            `;
        }
    } finally {
        // 隱藏載入中（如果有 hideLoading 函數）
        if (typeof hideLoading === 'function') {
            hideLoading();
        }
    }
};

// 重新定義 markAsRead 函數
window.markAsRead = function (messageId) {
    console.log('標記訊息為已讀:', messageId);

    try {
        // 獲取已讀訊息列表
        let readMessages = JSON.parse(localStorage.getItem('readMessages')) || [];

        if (!readMessages.includes(messageId)) {
            readMessages.push(messageId);
            localStorage.setItem('readMessages', JSON.stringify(readMessages));

            // 更新 UI
            const button = document.querySelector(`[onclick="markAsRead(${messageId})"]`);
            if (button) {
                button.textContent = '已讀';
                button.disabled = true;
                button.style.background = '#e9ecef';
                button.style.cursor = 'not-allowed';
            }

            if (typeof showMessage === 'function') {
                showMessage('已標記為已讀', 'success');
            }
            console.log('訊息已標記為已讀');
        }
    } catch (error) {
        console.error('標記已讀時發生錯誤:', error);
    }
};

console.log('快速修復腳本載入完成');
console.log('可用函數: loadPromotionMessages(), renderPromotionMessages(), markAsRead()');

// 自動嘗試修復
setTimeout(() => {
    const container = document.getElementById('promotions-view-content');
    if (container) {
        console.log('找到訊息容器，嘗試載入訊息...');
        window.loadPromotionMessages();
    } else {
        console.log('未找到訊息容器，等待頁面完全載入...');
    }
}, 2000);