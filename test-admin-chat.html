<!DOCTYPE html>
<html lang="zh-TW">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>管理員留言板測試</title>
    <link rel="stylesheet" href="web/css/style.css">
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }

        .test-container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 12px;
            padding: 20px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }

        h1 {
            text-align: center;
            color: #333;
            margin-bottom: 30px;
        }

        .test-actions {
            margin-bottom: 20px;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 8px;
        }

        .test-actions button {
            margin-right: 10px;
            margin-bottom: 10px;
        }
    </style>
</head>

<body>
    <div class="test-container">
        <h1>💬 管理員留言板測試</h1>

        <div class="test-actions">
            <h4>測試功能：</h4>
            <button onclick="createTestMessages()" class="btn btn-primary">創建測試留言</button>
            <button onclick="clearAllMessages()" class="btn btn-secondary">清除所有留言</button>
            <button onclick="loadChatManagement()" class="btn btn-success">重新載入</button>
        </div>

        <!-- 留言板管理 -->
        <div id="chat-management-admin" class="admin-tab-content active">
            <div class="chat-management-header">
                <div class="header-content">
                    <div class="header-icon">
                        <i class="icon">💬</i>
                    </div>
                    <div class="header-text">
                        <h3>留言板管理</h3>
                        <p>查看和回覆用戶留言</p>
                    </div>
                </div>
                <div class="chat-stats">
                    <div class="stat-item">
                        <span class="stat-number" id="unread-count">0</span>
                        <span class="stat-label">未讀訊息</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-number" id="total-chats">0</span>
                        <span class="stat-label">總對話數</span>
                    </div>
                </div>
            </div>

            <div class="chat-management-controls">
                <div class="filter-tabs">
                    <button class="filter-tab active" data-filter="all">全部</button>
                    <button class="filter-tab" data-filter="unread">未讀</button>
                    <button class="filter-tab" data-filter="replied">已回覆</button>
                </div>
                <div class="search-controls">
                    <input type="text" id="chat-search" placeholder="搜尋用戶名稱或訊息內容..." class="form-control">
                    <button id="chat-search-btn" class="btn btn-secondary">🔍 搜尋</button>
                </div>
            </div>

            <div class="chat-list-container">
                <div id="chat-list" class="chat-list">
                    <div class="no-chats">
                        <div class="no-chats-icon">💬</div>
                        <h4>暫無留言</h4>
                        <p>目前沒有用戶留言</p>
                    </div>
                </div>
            </div>

            <!-- 聊天對話視窗 -->
            <div id="chat-conversation" class="chat-conversation" style="display: none;">
                <div class="conversation-header">
                    <button id="back-to-list" class="btn btn-outline">← 返回列表</button>
                    <div class="user-info">
                        <h4 id="conversation-user-name">用戶名稱</h4>
                        <span id="conversation-user-info">聯絡資訊</span>
                    </div>
                    <div class="conversation-actions">
                        <button id="mark-resolved" class="btn btn-success">標記已解決</button>
                    </div>
                </div>

                <div class="conversation-messages" id="conversation-messages">
                    <!-- 對話訊息將在這裡顯示 -->
                </div>

                <div class="conversation-reply">
                    <form id="admin-reply-form">
                        <div class="reply-input-group">
                            <textarea id="admin-reply-input" placeholder="輸入回覆訊息..." rows="3" required></textarea>
                            <button type="submit" class="btn btn-primary">
                                <span>發送回覆</span>
                                <span class="send-icon">📤</span>
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 顯示訊息函數
        function showMessage(text, type = 'success') {
            const messageDiv = document.createElement('div');
            messageDiv.className = `alert alert-${type}`;
            messageDiv.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                padding: 15px 20px;
                border-radius: 5px;
                color: white;
                font-weight: bold;
                z-index: 9999;
                background: ${type === 'success' ? '#28a745' : type === 'error' ? '#dc3545' : '#ffc107'};
            `;
            messageDiv.textContent = text;
            document.body.appendChild(messageDiv);

            setTimeout(() => {
                messageDiv.remove();
            }, 3000);
        }

        // 創建測試留言
        function createTestMessages() {
            const testMessages = [
                {
                    id: Date.now() - 5000,
                    user_id: 'pharmacy_a',
                    user_name: '台南藥局A',
                    message: '請問這個產品有庫存嗎？我需要訂購50盒。',
                    type: 'user',
                    created_at: new Date(Date.now() - 3600000).toISOString(),
                    status: 'sent'
                },
                {
                    id: Date.now() - 4000,
                    user_id: 'pharmacy_b',
                    user_name: '健康藥局B',
                    message: '訂單什麼時候可以出貨？',
                    type: 'user',
                    created_at: new Date(Date.now() - 1800000).toISOString(),
                    status: 'sent'
                },
                {
                    id: Date.now() - 3000,
                    user_id: 'pharmacy_c',
                    user_name: '康復藥局C',
                    message: '謝謝您的回覆，我了解了。',
                    type: 'user',
                    created_at: new Date(Date.now() - 900000).toISOString(),
                    status: 'sent',
                    replied: true
                },
                {
                    id: Date.now() - 2000,
                    user_id: 'pharmacy_a',
                    user_name: '台南藥局A',
                    message: '另外想問一下配送時間大概多久？',
                    type: 'user',
                    created_at: new Date(Date.now() - 600000).toISOString(),
                    status: 'sent'
                },
                {
                    id: Date.now() - 1000,
                    user_id: 'pharmacy_d',
                    user_name: '新生藥局D',
                    message: '你好，我是新註冊的用戶，請問如何下單？',
                    type: 'user',
                    created_at: new Date(Date.now() - 300000).toISOString(),
                    status: 'sent'
                }
            ];

            localStorage.setItem('chatHistory', JSON.stringify(testMessages));
            loadChatManagement();
            showMessage('測試留言已創建', 'success');
        }

        // 清除所有留言
        function clearAllMessages() {
            localStorage.removeItem('chatHistory');
            loadChatManagement();
            showMessage('所有留言已清除', 'success');
        }

        // 載入留言板管理的所有函數（從 app.js 複製）
        function updateChatStats() {
            const chatHistory = JSON.parse(localStorage.getItem('chatHistory')) || [];

            const userChats = {};
            chatHistory.forEach(msg => {
                if (!userChats[msg.user_id]) {
                    userChats[msg.user_id] = {
                        user_id: msg.user_id,
                        user_name: msg.user_name,
                        messages: [],
                        last_message: null,
                        unread_count: 0
                    };
                }
                userChats[msg.user_id].messages.push(msg);
                userChats[msg.user_id].last_message = msg;

                if (msg.type === 'user' && !msg.replied) {
                    userChats[msg.user_id].unread_count++;
                }
            });

            const totalChats = Object.keys(userChats).length;
            const unreadCount = Object.values(userChats).reduce((sum, chat) => sum + chat.unread_count, 0);

            const unreadCountEl = document.getElementById('unread-count');
            const totalChatsEl = document.getElementById('total-chats');

            if (unreadCountEl) unreadCountEl.textContent = unreadCount;
            if (totalChatsEl) totalChatsEl.textContent = totalChats;
        }

        function loadChatList(filter = 'all') {
            const chatHistory = JSON.parse(localStorage.getItem('chatHistory')) || [];
            const chatListEl = document.getElementById('chat-list');

            if (!chatListEl) return;

            const userChats = {};
            chatHistory.forEach(msg => {
                if (!userChats[msg.user_id]) {
                    userChats[msg.user_id] = {
                        user_id: msg.user_id,
                        user_name: msg.user_name,
                        messages: [],
                        last_message: null,
                        last_message_time: null,
                        unread_count: 0,
                        status: 'read'
                    };
                }
                userChats[msg.user_id].messages.push(msg);

                if (!userChats[msg.user_id].last_message_time ||
                    new Date(msg.created_at) > new Date(userChats[msg.user_id].last_message_time)) {
                    userChats[msg.user_id].last_message = msg;
                    userChats[msg.user_id].last_message_time = msg.created_at;
                }

                if (msg.type === 'user' && !msg.replied) {
                    userChats[msg.user_id].unread_count++;
                    userChats[msg.user_id].status = 'unread';
                }
            });

            let chatList = Object.values(userChats).sort((a, b) =>
                new Date(b.last_message_time) - new Date(a.last_message_time)
            );

            if (filter === 'unread') {
                chatList = chatList.filter(chat => chat.unread_count > 0);
            } else if (filter === 'replied') {
                chatList = chatList.filter(chat => chat.unread_count === 0);
            }

            if (chatList.length === 0) {
                chatListEl.innerHTML = `
                    <div class="no-chats">
                        <div class="no-chats-icon">💬</div>
                        <h4>暫無留言</h4>
                        <p>目前沒有用戶留言</p>
                    </div>
                `;
                return;
            }

            chatListEl.innerHTML = chatList.map(chat => `
                <div class="chat-item ${chat.status}" onclick="openChatConversation('${chat.user_id}')">
                    <div class="chat-avatar">👤</div>
                    <div class="chat-info">
                        <div class="chat-user-name">${chat.user_name}</div>
                        <div class="chat-last-message">${chat.last_message ? chat.last_message.message : '暫無訊息'}</div>
                    </div>
                    <div class="chat-meta">
                        <div class="chat-time">${chat.last_message_time ? new Date(chat.last_message_time).toLocaleString('zh-TW', {
                month: 'short',
                day: 'numeric',
                hour: '2-digit',
                minute: '2-digit'
            }) : ''}</div>
                        <div class="chat-status ${chat.status}">
                            ${chat.status === 'unread' ? `未讀 (${chat.unread_count})` : '已讀'}
                        </div>
                    </div>
                </div>
            `).join('');
        }

        function openChatConversation(userId) {
            const chatHistory = JSON.parse(localStorage.getItem('chatHistory')) || [];
            const userMessages = chatHistory.filter(msg => msg.user_id === userId);

            if (userMessages.length === 0) return;

            const userName = userMessages[0].user_name;

            document.getElementById('chat-list').style.display = 'none';
            const conversationEl = document.getElementById('chat-conversation');
            conversationEl.style.display = 'flex';

            document.getElementById('conversation-user-name').textContent = userName;
            document.getElementById('conversation-user-info').textContent = `用戶ID: ${userId}`;

            const messagesEl = document.getElementById('conversation-messages');
            messagesEl.innerHTML = userMessages.map(msg => `
                <div class="message-content ${msg.type}-message">
                    <div class="avatar">${msg.type === 'user' ? '👤' : '👨‍💼'}</div>
                    <div class="message-bubble ${msg.type}">
                        <p>${msg.message}</p>
                        <span class="message-time">${new Date(msg.created_at).toLocaleString('zh-TW')}</span>
                    </div>
                </div>
            `).join('');

            messagesEl.scrollTop = messagesEl.scrollHeight;
            conversationEl.dataset.userId = userId;
        }

        function loadChatManagement() {
            updateChatStats();
            loadChatList();
            setupChatManagementEvents();
        }

        function setupChatManagementEvents() {
            document.querySelectorAll('.filter-tab').forEach(tab => {
                tab.addEventListener('click', function () {
                    document.querySelectorAll('.filter-tab').forEach(t => t.classList.remove('active'));
                    this.classList.add('active');
                    loadChatList(this.dataset.filter);
                });
            });

            const backBtn = document.getElementById('back-to-list');
            if (backBtn) {
                backBtn.addEventListener('click', function () {
                    document.getElementById('chat-conversation').style.display = 'none';
                    document.getElementById('chat-list').style.display = 'block';
                    loadChatList();
                });
            }

            const replyForm = document.getElementById('admin-reply-form');
            if (replyForm) {
                replyForm.addEventListener('submit', function (e) {
                    e.preventDefault();
                    sendAdminReply();
                });
            }
        }

        function sendAdminReply() {
            const conversationEl = document.getElementById('chat-conversation');
            const userId = conversationEl.dataset.userId;
            const replyInput = document.getElementById('admin-reply-input');
            const message = replyInput.value.trim();

            if (!message || !userId) return;

            const replyMessage = {
                id: Date.now(),
                user_id: userId,
                user_name: document.getElementById('conversation-user-name').textContent,
                message: message,
                type: 'admin',
                created_at: new Date().toISOString(),
                status: 'sent'
            };

            let chatHistory = JSON.parse(localStorage.getItem('chatHistory')) || [];
            chatHistory.push(replyMessage);

            chatHistory = chatHistory.map(msg => {
                if (msg.user_id === userId && msg.type === 'user' && !msg.replied) {
                    msg.replied = true;
                }
                return msg;
            });

            localStorage.setItem('chatHistory', JSON.stringify(chatHistory));

            const messagesEl = document.getElementById('conversation-messages');
            const replyDiv = document.createElement('div');
            replyDiv.className = 'message-content admin-message';
            replyDiv.innerHTML = `
                <div class="avatar">👨‍💼</div>
                <div class="message-bubble admin">
                    <p>${message}</p>
                    <span class="message-time">${new Date().toLocaleString('zh-TW')}</span>
                </div>
            `;
            messagesEl.appendChild(replyDiv);
            messagesEl.scrollTop = messagesEl.scrollHeight;

            replyInput.value = '';
            updateChatStats();
            showMessage('回覆已發送', 'success');
        }

        // 初始化
        document.addEventListener('DOMContentLoaded', function () {
            loadChatManagement();
        });
    </script>
</body>

</html>