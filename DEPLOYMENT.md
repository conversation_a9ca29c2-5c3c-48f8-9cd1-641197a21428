# 藥房管理系統部署指南

## 域名配置
- **開發環境**: http://localhost:8080
- **生產環境**: https://order.53617503.xyz

## 部署步驟

### 1. 本地開發
```bash
# 直接運行，會自動使用 localhost:8080
cargo run --release
```

### 2. 生產環境部署

#### 方法一：使用部署腳本
```bash
# 給腳本執行權限
chmod +x deploy.sh

# 建置生產版本
./deploy.sh production

# 將 dist 目錄的內容上傳到服務器
```

#### 方法二：手動部署
1. 將 `web` 目錄的所有檔案上傳到服務器
2. 確保服務器配置正確指向 `index.html`
3. 系統會自動偵測環境並使用正確的 API 端點

### 3. 服務器配置

#### Nginx 配置範例
```nginx
server {
    listen 80;
    listen 443 ssl;
    server_name order.53617503.xyz;
    
    # SSL 配置
    ssl_certificate /path/to/your/certificate.crt;
    ssl_certificate_key /path/to/your/private.key;
    
    # 靜態檔案
    location / {
        root /path/to/your/web/files;
        try_files $uri $uri/ /index.html;
        index index.html;
    }
    
    # API 代理（如果後端在不同端口）
    location /api/ {
        proxy_pass http://localhost:8080;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}
```

#### Apache 配置範例
```apache
<VirtualHost *:80>
    ServerName order.53617503.xyz
    DocumentRoot /path/to/your/web/files
    
    # 重寫規則支援 SPA
    <Directory "/path/to/your/web/files">
        RewriteEngine On
        RewriteBase /
        RewriteRule ^index\.html$ - [L]
        RewriteCond %{REQUEST_FILENAME} !-f
        RewriteCond %{REQUEST_FILENAME} !-d
        RewriteRule . /index.html [L]
    </Directory>
    
    # API 代理
    ProxyPass /api/ http://localhost:8080/api/
    ProxyPassReverse /api/ http://localhost:8080/api/
</VirtualHost>
```

## 環境變數

系統會自動根據域名判斷環境：
- `localhost` 或 `127.0.0.1` → 開發環境
- `order.53617503.xyz` → 生產環境

## 測試部署

1. **本地測試**：
   ```bash
   # 啟動本地服務器
   cargo run --release
   # 訪問 http://localhost:8080
   ```

2. **生產環境測試**：
   ```bash
   # 訪問 https://order.53617503.xyz
   # 檢查瀏覽器控制台確認 API_BASE 設定正確
   ```

## 故障排除

### 常見問題

1. **CORS 錯誤**：
   - 確保後端允許來自 `order.53617503.xyz` 的請求
   - 檢查 Rust 後端的 CORS 設定

2. **API 請求失敗**：
   - 檢查瀏覽器控制台的 API_BASE 設定
   - 確認後端服務正在運行
   - 檢查防火牆設定

3. **靜態檔案載入失敗**：
   - 確認檔案路徑正確
   - 檢查服務器權限設定

### 調試模式

在瀏覽器控制台中檢查：
```javascript
console.log("API_BASE:", window.CONFIG.API_BASE);
console.log("DEBUG:", window.CONFIG.DEBUG);
```

## 安全性考量

1. **HTTPS**：生產環境必須使用 HTTPS
2. **CORS**：正確配置跨域請求
3. **API 安全**：確保 API 端點有適當的認證和授權
4. **檔案權限**：設定適當的檔案系統權限

## 監控和日誌

- 檢查服務器訪問日誌
- 監控 API 響應時間
- 設定錯誤報告機制